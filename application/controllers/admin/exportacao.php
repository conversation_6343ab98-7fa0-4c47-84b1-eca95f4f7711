<?php

/*
 * Imóvel
 */

class Exportacao extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('usuarios_model');
		$this->usuarios_model->verify_login();
		$this->load->model('imoveis_model');
		$this->load->model('cidades_model');
		$this->load->model('bairros_model');
		$this->load->model('tipos_model');
		$this->load->library('session');
		$this->load->helper('portais');

		$busca = array(
			'page' => 0,
			'cidade_id' => '',
			'bairro_id' => '',
			'id' => '',
			'where' => ''
		);

		$informacoes = array(
			'pagina' => 'exportacao',
			'titulo' => 'Exportação',
			'filtragem' => 'Todos os imóveis',
			'validacao' => 'imovel',
			'abreviacao_banco' => 'I',
			'tab' => 'identificacao'
		);

		$mensagens = array(
			'alteracao' => '<div class="alert alert-success fade in"><a class="close" data-dismiss="alert" href="#">&times;</a> Imóvel alterado com sucesso</div>',
			'inclusao' => '<div class="alert alert-success fade in"><a class="close" data-dismiss="alert" href="#">&times;</a> Imóvel cadastrado com sucesso</div>',
			'deletar' => '<div class="alert alert-success fade in"><a class="close" data-dismiss="alert" href="#">&times;</a> Imóvel deletado com sucesso</div>',
			'erro_deletar' => '<div class="alert alert-success fade in"><a class="close" data-dismiss="alert" href="#">&times;</a> Imóvel deletado com sucesso</div>'
		);

		$informacoes_ordenar = array(
			'id' => 'ID',
			'referencia' => 'Referência',
			'cidade_id' => 'Cidade',
			'bairro' => 'Bairro',
			'area_total' => 'Área',
			'dormitorios' => 'Quartos',
			'garagens' => 'Garagem'
		);

		$config = array(
			'busca' => $busca,
			'informacoes' => $informacoes,
			'mensagens' => $mensagens,
			'informacoes_ordenar' => $informacoes_ordenar
		);

		$this->data = $config;
	}

	/* Adicionar imóvel aos selecionados */
	public function adicionar()
	{
		$data['ci'] = $this;

		if ($this->session->userdata('permissao_alterar') == 1) {
			$id = $_GET['refs'];
			$pg = $_GET['pg'];
			$site = $_GET['parceiro'];

			$imobiliaria_id = $this->session->userdata('imobiliaria_id');

			$data = array(
				'imovel_id' => $id,
				'imobiliaria_id' => $imobiliaria_id,
				$pg => 1,
				'site' => $site
			);
			$this->db->insert('imoveis_sites_parceiros', $data);

			$this->gravar_log();

			if ($site == 'imoveisempinhais' && strpos($this->session->userdata['portais'], 'imoveisempinhais') > -1) {
				portal_atualizar_imovel('imoveisempinhais', $id, $imobiliaria_id);
			}

			// Função desativada pois demora pra atualizar o imóvel no portal, o imóvel será inserido na próxima atualização feita pelo servidor de tarefas
			/*
			if ($site=='imoveisemcuritiba' && strpos($this->session->userdata['portais'], 'imoveisemcuritiba') > -1) {
				portal_atualizar_imovel('imoveisemcuritiba', $id, $imobiliaria_id);
			}
			*/
		}
	}

	/**
	 * Adicionar vários imóveis de uma vez só na listagem
	 */
	public function adicionar_batch()
	{
		$data['ci'] = $this;
		$site = $_POST['site'];
		$pg = $_POST['pg'];
		$tmp = $_POST['id'];
		$imoveis = explode(',', $tmp);

		$imobiliaria_id = $this->session->userdata('imobiliaria_id');

		// Deletar os já cadastrados, para evitar duplicações
		$this->remover_batch();

		// Remover a flag que indica que todos estão selecionados
		$data = array(
			'imobiliaria_id' => $imobiliaria_id,
			$pg => 1,
			'site' => $site
		);
		$this->db->delete('sites_parceiros_selecionar_todos', $data);

		// Criar uma array com todos os imóveis a serem inseridos para fazer apenas um insert
		$array = [];
		for ($i = 0; $i < sizeof($imoveis); $i++) {
			array_push($array, array(
				'imobiliaria_id' => $imobiliaria_id,
				'site' => $site,
				'imovel_id' => $imoveis[$i],
				$pg => 1
			));
		}

		$qtd_contratados = $this->db->select('imoveis_permitidos, portal')
			->from('portais_quantidades')
			->where('imobiliaria_id', $imobiliaria_id)
			->where('portal', $site)
			->get()
			->result_array();

		if (sizeof($qtd_contratados) > 0 && $qtd_contratados[0]['imoveis_permitidos'] != 0) {
			$qtd_contratados = $qtd_contratados[0]['imoveis_permitidos'];
		} else {
			$qtd_contratados = 999999999999999999;
		}

		$qtde_imoveis = sizeof($array);

		$this->db->select('I.id');
		$this->db->distinct();
		$this->db->from('imoveis I');
		$this->db->join('cidades C', 'C.id = I.cidade_id', 'left');
		$this->db->join('bairros B', 'B.id = I.bairro_id', 'left');
		$this->db->join('tipos T', 'T.id = I.tipo_id');
		$this->db->join('imobiliarias IM', 'IM.id = I.imobiliaria_id');
		$this->db->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left');
		$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $site . '\'');
		$this->db->where('M.enviar_para_portais', 1);
		$this->db->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE);
		$this->db->where('IM.ativa', 1);
		$this->db->order_by('I.id');

		$query =  $this->db->get();
		$selecionados = $query->result_array();
		$selecionados = sizeof($selecionados);

		if (($qtde_imoveis + $selecionados) > $qtd_contratados) {
			return $this->output
				->set_content_type('application/json')
				->set_status_header(500)
				->set_output(json_encode(array(
					'mensagem' => 'Número de imóveis selecionados excede o plano contratado',
				)));
		}

		$this->db->insert_batch('imoveis_sites_parceiros', $array);

		echo 'OK';
	}

	/* Destacar imóvel */
	public function destacar()
	{
		$data['ci'] = $this;
		if ($this->session->userdata('permissao_alterar') == 1) {
			$id = $_GET['refs'];
			$pg = $_GET['pg'];
			$site = $_GET['parceiro'];
			$imobiliaria_id = $this->session->userdata('imobiliaria_id');

			$data = array(
				'imobiliaria_id' => $imobiliaria_id,
				'site' => $site,
				$pg => 1,
				'imovel_id' => $id
			);
			$this->db->delete('imoveis_sites_parceiros', $data);

			$data = array(
				'imovel_id' => $id,
				'imobiliaria_id' => $imobiliaria_id,
				$pg => 1,
				'site' => $site
			);
			$this->db->insert('imoveis_sites_parceiros', $data);

			$data = array('destacar' => 1);
			$this->db->where('imovel_id', $id);
			$this->db->where($pg, 1);
			$this->db->where('site', $site);
			$this->db->update('imoveis_sites_parceiros', $data);

			$this->gravar_log();

			if (strpos($this->session->userdata['portais'], 'imoveisempinhais') > -1) {
				portal_destacar_imovel('imoveisempinhais', $id);
			}
		}
	}

	/* Destacar imóvel 2 (imovel web) */
	public function destacar2()
	{
		$data['ci'] = $this;
		if ($this->session->userdata('permissao_alterar') == 1) {
			$id = $_GET['refs'];
			$pg = $_GET['pg'];
			$opcao = $_GET['opcao'];
			$site = $_GET['parceiro'];

			$data = array(
				'imobiliaria_id' => $this->session->userdata('imobiliaria_id'),
				'site' => $site,
				$pg => 1,
				'imovel_id' => $id
			);
			$this->db->delete('imoveis_sites_parceiros', $data);

			$data = array(
				'imovel_id' => $id,
				'imobiliaria_id' => $this->session->userdata('imobiliaria_id'),
				$pg => 1,
				'site' => $site
			);
			$this->db->insert('imoveis_sites_parceiros', $data);

			$data = array('destacar' => $opcao);
			$this->db->where('imovel_id', $id);
			$this->db->where($pg, 1);
			$this->db->where('site', $site);
			$this->db->update('imoveis_sites_parceiros', $data);

			$this->gravar_log();
		}
	}

	/**
	 * Destacar vários imóveis de uma vez só na listagem
	 */
	public function destacar_batch()
	{
		$data['ci'] = $this;
		$site = $_POST['parceiro'];
		$pg = $_POST['pg'];
		$opcao = $_POST['o'];
		$tmp = $_POST['ids'];
		$imoveis = explode(',', $tmp);

		$destacar = 1;
		if ($opcao == 'remover_destaque' || $opcao == 'nao') {
			$destacar = 0;
		} else if ($opcao == 'todos_especial') {
			$destacar = 2;
		} else if ($opcao == 'todos_especial2') {
			$destacar = 3;
		} else if ($opcao == 'todos_especial3') {
			$destacar = 4;
		}

		$data = array(
			'destacar' => $destacar
		);
		$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'))
			->where('site', $site)
			->where($pg, 1)
			->where_in('imovel_id', $imoveis)
			->update('imoveis_sites_parceiros', $data);

		// Bucar o total de destacados
		if (isset($site) && ($site == 'zap' || $site == 'mercadolivre')) {
			$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $site . '\' AND (IP.destacar=2)');
		} else {
			$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $site . '\' AND (IP.destacar=1)');
		}
		$this->db->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE);

		$this->db->select('I.id')
			->distinct()
			->from('imoveis I')
			->join('tipos T', 'T.id = I.tipo_id')
			->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
			->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
			->where('M.enviar_para_portais', 1)
			->where('IM.ativa', 1);

		$data['destacados'] =  $this->db->count_all_results();


		// Bucar o total de super destacados
		if (isset($site) && ($site == 'zap' || $site == 'mercadolivre')) {
			$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $site . '\' AND (IP.destacar=3)');
		} else {
			$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $site . '\' AND (IP.destacar=2)');
		}
		$this->db->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE);

		$this->db->select('I.id')
			->distinct()
			->from('imoveis I')
			->join('tipos T', 'T.id = I.tipo_id')
			->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
			->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
			->where('M.enviar_para_portais', 1)
			->where('IM.ativa', 1);

		$data['super_destacados'] =  $this->db->count_all_results();

		if (isset($site) && (in_array($site, array('vivareal', 'buscacuritiba')))) {
			$data['destaque_especial'] =  $this->db->select('I.id')
				->distinct()
				->from('imoveis I')
				->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $site . '\' AND (IP.destacar=3)')
				->join('tipos T', 'T.id = I.tipo_id')
				->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
				->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
				->where('M.enviar_para_portais', 1)
				->where('IM.ativa', 1)
				->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE)
				->count_all_results();

			$data['destaque_premium'] =  $this->db->select('I.id')
				->distinct()
				->from('imoveis I')
				->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $site . '\' AND (IP.destacar=4)')
				->join('tipos T', 'T.id = I.tipo_id')
				->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
				->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
				->where('M.enviar_para_portais', 1)
				->where('IM.ativa', 1)
				->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE)
				->count_all_results();
		}

		$this->output
			->set_content_type('application/json')
			->set_output(json_encode($data));
	}

	/* Destacar todos (adicionar ou remover) */
	public function destacar_todos()
	{
		$data['ci'] = $this;
		if ($this->session->userdata('permissao_alterar') == 1) {
			$site = $_GET['parceiro'];
			$pg = $_GET['pg'];
			$opcao = $_GET['o'];

			$data = array(
				'destacar' => 1
			);

			$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
			$this->db->where('site', $site);
			$this->db->update('imoveis_sites_parceiros', $data);

			if ($opcao == 'destacar' || $opcao == 'todos') {
				$data = array(
					'destacar' => 1
				);

				$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
				$this->db->where('site', $site);
				$this->db->update('imoveis_sites_parceiros', $data);
			} else if ($opcao == 'remover_destaque' || $opcao == 'nao') {
				$data = array(
					'destacar' => 0
				);

				$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
				$this->db->where('site', $site);
				$this->db->update('imoveis_sites_parceiros', $data);
			} else if ($opcao == 'todos_especial') {
				$data = array(
					'destacar' => 2
				);

				$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
				$this->db->where('site', $site);
				$this->db->update('imoveis_sites_parceiros', $data);
			}

			//echo $this->db->last_query();

			$this->gravar_log();
		}
	}

	/* Remover imóvel aos selecionados */
	public function remover()
	{
		$data['ci'] = $this;
		if ($this->session->userdata('permissao_alterar') == 1) {
			$id = $_GET['refs'];
			$pg = $_GET['pg'];
			$site = $ref = $_GET['parceiro'];
			$imobiliaria_id = $this->session->userdata('imobiliaria_id');


			//Verifica se o selecionar todos está ativado
			$this->db->select('ativo');
			$this->db->from('sites_parceiros_selecionar_todos');
			$this->db->where('site', $site);
			$this->db->where($pg, 1);
			$this->db->where('imobiliaria_id', $imobiliaria_id);
			$query = $this->db->get();
			echo '<p>' . $this->db->last_query() . '</p>';
			$selecionar_todos = $query->result_array();

			$buscar_todos = 0;
			if (isset($selecionar_todos[0]) && $selecionar_todos[0]['ativo'] == '1') {
				//$buscar_todos = 1;
			} else {
				$buscar_todos = 0;
			}

			if ($buscar_todos == 1) {
				//Remover todos os imóveis na tabela de selecionados
				$data = array(
					'imobiliaria_id' => $imobiliaria_id,
					$pg => 1,
					'site' => $site
				);
				$this->db->delete('imoveis_sites_parceiros', $data);
				echo '<p>' . $this->db->last_query() . '</p>';

				$data = array(
					'imobiliaria_id' => $imobiliaria_id,
					$pg => 1,
					'site' => $site
				);
				$this->db->delete('sites_parceiros_selecionar_todos', $data);

				//Adicionar todos os imóveis na tabela de selecionados
				$this->db->select('id AS imovel_id, concat(\'\',\'' . $site . '\') as site', FALSE);
				$this->db->select('concat(\'\',\'1\') as ' . $pg, FALSE);
				$this->db->select('imobiliaria_id');
				$this->db->from('imoveis');
				$this->db->where('imobiliaria_id', $imobiliaria_id);
				$query = $this->db->get();
				echo '<p>' . $this->db->last_query() . '</p>';
				$imoveis = $query->result_array();

				$this->db->insert_batch('imoveis_sites_parceiros', $imoveis);
				echo '<p>' . $this->db->last_query() . '</p>';
			} else {
				$data = array(
					'imobiliaria_id' => $imobiliaria_id,
					'imovel_id' => $id,
					$pg => 1,
					'site' => $site
				);
				$this->db->delete('imoveis_sites_parceiros', $data);
				echo '<p>' . $this->db->last_query() . '</p>';
			}

			$data = array(
				'imovel_id' => $id,
				$pg => 1,
				'site' => $site
			);
			$this->db->delete('imoveis_sites_parceiros', $data);

			$data = array(
				'imobiliaria_id' => $this->session->userdata('imobiliaria_id'),
				$pg => 1,
				'site' => $site
			);
			$this->db->delete('sites_parceiros_selecionar_todos', $data);

			$this->gravar_log();

			if (strpos($this->session->userdata['portais'], 'imoveisempinhais') > -1) {
				portal_deletar_imovel('imoveisempinhais', $id);
			}
		}
	}

	/* Remover imóvel dos destaques */
	public function remover_destaque()
	{
		$data['ci'] = $this;
		if ($this->session->userdata('permissao_alterar') == 1) {
			$id = $_GET['refs'];
			$pg = $_GET['pg'];
			$site = $ref = $_GET['parceiro'];
			$imobiliaria_id = $this->session->userdata('imobiliaria_id');

			$data = array('destacar' => 0);
			$this->db->where('imovel_id', $id);
			$this->db->where('imobiliaria_id', $imobiliaria_id);
			$this->db->where($pg, 1);
			$this->db->where('site', $site);
			$this->db->update('imoveis_sites_parceiros', $data);

			$this->gravar_log();

			if (strpos($this->session->userdata['portais'], 'imoveisempinhais') > -1) {
				portal_destacar_imovel('imoveisempinhais', $id, 'remover');
			}
		}
	}

	/**
	 * Remover vários imóveis de uma vez só na listagem
	 */
	public function remover_batch()
	{
		$data['ci'] = $this;
		$pg = $_POST['pg'];
		$tmp = $_POST['id'];
		$imoveis = explode(',', $tmp);
		$site = $_POST['site'];
		$imobiliaria_id = $this->session->userdata['imobiliaria_id'];

		// Remover a flag que indica que todos estão selecionados
		$data = array(
			'imobiliaria_id' => $imobiliaria_id,
			$pg => 1,
			'site' => $site
		);
		$this->db->delete('sites_parceiros_selecionar_todos', $data);

		// Deletar os já cadastrados, para evitar duplicações
		$this->db->from('imoveis_sites_parceiros')
			->where($pg, 1)
			->where('imobiliaria_id', $imobiliaria_id)
			->where_in('imovel_id', $imoveis)
			->where('site', $site)
			->delete();
	}

	public function salvar_qtd_contratados()
	{
		if (isset($_POST['portal']) && $_POST['portal'] != '') {
			$this->db->from('portais_quantidades')
				->where('imobiliaria_id', $this->session->userdata['imobiliaria_id'])
				->where('portal', $_POST['portal'])
				->delete();

			$dados = array(
				'imobiliaria_id' => $this->session->userdata('imobiliaria_id'),
				'portal' => $_POST['portal'],
				'imoveis_permitidos' => (isset($_POST['qtd']) && $_POST['qtd'] != '') ? $_POST['qtd'] : ''
			);
			$this->db->insert('portais_quantidades', $dados);
		}
	}

	/**
	 * Retorna o número de imóveis selecionados
	 */
	public function selecionados()
	{
		$site = $_GET['site'];
		$pg = $_GET['pg'];

		$selecionados = $this->db->where('site', $site)
			->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'))
			->where($pg, 1)
			->count_all_results('imoveis_sites_parceiros');

		echo $selecionados;
	}

	/* Selecionar todos (adicionar ou remover) */
	public function selecionar_todos()
	{
		$data['ci'] = $this;
		if ($this->session->userdata('permissao_alterar') == 1) {
			$site = $_POST['parceiro'];
			$pg = $_POST['pg'];
			$opcao = $_POST['o'];

			$imobiliaria_id = $this->session->userdata('imobiliaria_id');

			$qtd_contratados = $this->db->select('imoveis_permitidos, portal')
				->from('portais_quantidades')
				->where('imobiliaria_id', $imobiliaria_id)
				->where('portal', $site)
				->get()
				->result_array();
			if (sizeof($qtd_contratados) > 0 && $qtd_contratados[0]['imoveis_permitidos'] != 0) {
				$qtd_contratados = $qtd_contratados[0]['imoveis_permitidos'];
			} else {
				$qtd_contratados = 999999999999999999;
			}

			$imoveis = explode(',', $_POST['refs']);
			$qtde_imoveis = sizeof($imoveis);

			if ($qtde_imoveis > $qtd_contratados && $opcao != 'remover') {
				return $this->output
					->set_content_type('application/json')
					->set_status_header(500)
					->set_output(json_encode(array(
						'mensagem' => 'Número de imóveis selecionados excede o plano contratado',
					)));
			}

			$this->db->select();
			$this->db->from('imoveis_sites_parceiros');
			$this->db->where('imobiliaria_id', $imobiliaria_id);
			$this->db->where('site', $site);
			$this->db->where('destacar', 1);
			$this->db->where($pg, 1);
			$query = $this->db->get();
			$reservados = $query->result_array();

			$this->db->where('imobiliaria_id', $imobiliaria_id);
			$this->db->where('site', $site);
			$this->db->where($pg, 1);
			$this->db->delete('imoveis_sites_parceiros');

			if ($opcao == 'adicionar') {
				$imoveis = explode(',', $_POST['refs']);
				$data = array();
				$x = 0;
				foreach ($imoveis as $imovel) {
					$data[$x]['imovel_id'] = $imovel;
					$data[$x]['imobiliaria_id'] = $imobiliaria_id;
					$data[$x]['site'] = $site;
					$data[$x][$pg] = 1;
					$x++;
				}
				$this->db->insert_batch('imoveis_sites_parceiros', $data);

				$query = 'INSERT INTO sites_parceiros_selecionar_todos (site,ativo,' . $pg . ',imobiliaria_id) VALUES (\'' . $site . '\',1,1,' . $imobiliaria_id . ')';

				$this->db->query($query);

				foreach ($reservados as $imovel) {
					$imovel['destacar'] = 1;
					$x++;

					$this->db->where('imovel_id', $imovel['imovel_id']);
					$this->db->where($pg, 1);
					$this->db->update('imoveis_sites_parceiros', $imovel);
				}
			} else {
				$data = array(
					'imobiliaria_id' => $imobiliaria_id,
					$pg => 1,
					'site' => $site
				);
				$this->db->delete('sites_parceiros_selecionar_todos', $data);

				$this->db->delete('imoveis_sites_parceiros', $data);
			}

			$this->gravar_log();
		}
	}

	/*
	 * Página inicial, busca os imóveis para seleção
	 */
	public function selecionar()
	{
		$data['ci'] = $this;

		if ($this->session->userdata('nivel') == 8) redirect('admin/main');

		if ($this->session->userdata('permissao_alterar') == 1) {

			$data['pagina'] = 'exportacao';

			$data['campos_busca'] = array(
				'site' => '',
				'pg' => '',
				'referencia' => '',
				'endereco' => '',
				'tipo_id' => '',
				'trabalhar_como_id' => '',
				'modo' => '',
				'corretor_angariador_id' => '',
				'agencia' => '',
				'corretor_galvao' => ''
			);

			$galvoes = array('1277', '1228', '1229', '1233', '1234', '1235', '1236', '1237', '1238', '1239', '1240', '1241', '1242', '1243', '1244', '1246', '1250', '1251', '1258', '1265');
			$data['galvao'] = false;
			if (in_array($this->session->userdata('imobiliaria_id'), $galvoes)) $data['galvao'] = true;

			if (isset($_POST['site'])) {
				$data['campos_busca']['site'] = $_POST['site'];
			}

			$busca = array();

			if (isset($_POST['pg']) && $_POST['pg'] != '') {
				$data['campos_busca']['pg'] = $_POST['pg'];
				$busca['I.' . $_POST['pg']] = 1;
			} else {
				$data['campos_busca']['pg'] = 'tipo_venda';
				$busca['I.tipo_venda'] = 1;
			}

			if ($this->session->userdata['imobiliaria_id'] == 18) {
				$busca['I.trabalhar_como_id'] = 61;
			}

			if (isset($_POST['tipo_id']) && $_POST['tipo_id'] != '') {
				$data['campos_busca']['tipo_id'] = $_POST['tipo_id'];
				$busca['tipo_id'] = $_POST['tipo_id'];
			}

			if (isset($_POST['modo']) && $_POST['modo'] != '') {
				$data['campos_busca']['trabalhar_como_id'] = $_POST['modo'];
				$busca['I.trabalhar_como_id'] = $_POST['modo'];
			}

			if (isset($_POST['corretor_galvao']) && $_POST['corretor_galvao'] != '') {
				$data['campos_busca']['corretor_galvao'] = $_POST['corretor_galvao'];
			}

			if (isset($_POST['agencia']) && $_POST['agencia'] != '') {
				$data['campos_busca']['agencia'] = $_POST['agencia'];
			}

			if (isset($_POST['endereco']) && $_POST['endereco'] != '') {
				$data['campos_busca']['endereco'] = $_POST['endereco'];
			}

			$data['sites'] = array(
				'casamineira' => 'Casa Mineira',
				'imovelweb' => 'Imóvel Web',
				'minhaprimeiracasa' => 'Minha Primeira Casa',
				'vivareal' => 'Grupo ZAP',
				'imovelmagazine' => 'Imóvel Magazine',
				'redeimoveis' => 'Rede Imóveis',
				'chavesnamao' => 'Chaves na Mão',
				'imoviewtv' => 'Imoview Tv',
				'canaldoimovel' => 'Canal do Imóvel',
				'mercadolivre' => 'Mercado Livre',
				'properati' => 'Properati',
				'chavefacilnacional' => 'Chave Fácil Nacional',
				'rodadadeimoveis' => 'Rodada de Imóveis',
				'luxury' => 'Luxury Estate',
				'imovomap' => 'Portal ImovoMAP',
				'attria' => 'Portal Attria',
				'facebook' => 'Portal Catálogo',
				'creci' => 'Creci',
				'redeurbana' => 'Rede Urbana Integração',
				'loft' => 'Loft',
				'olx' => 'Olx',
				'buscacuritiba' => 'Busca Curitiba',
			);

			if (strpos($this->session->userdata['portais'], 'imoveisempinhais') > -1) {
				$data['sites']['imoveisempinhais'] = 'Imóveis em Pinhais';
			}

			if (strpos($this->session->userdata['portais'], 'imoveisemcuritiba') > -1) {
				$data['sites']['imoveisemcuritiba'] = 'Imóveis em Curitiba';
			}

			asort($data['sites']);
			$data['sites'] = array('' => 'Selecione') + $data['sites'];

			$data['pgs'] = array(
				'' => 'Selecione',
				'tipo_venda' => 'Venda',
				'tipo_locacao' => 'Locação',
				'tipo_lancamento' => 'Lançamento',
				'tipo_temporada' => 'Temporada'
			);

			$data['tipos_destaque'] = array(
				'0' => 'Não destacar',
				'1' => 'Destacar',
				'2' => 'Home'
			);

			$data['tipos_destaque_imovelweb'] = array(
				'0' => 'Não destacar',
				'1' => 'Destacar',
				'2' => 'Super Destaque'
			);

			$data['tipos_destaque2'] = array(
				'1' => 'Simples',
				'2' => 'Destaque',
				'3' => 'Super Destaque'
			);

			$data['tipos_destaque_mercadolivre'] = array(
				'1' => 'Silver',
				'2' => 'Gold',
				'3' => 'Gold Premium'
			);

			$data['tipos_destaque3'] = array(
				'0' => 'Nenhum',
				'1' => 'Destaque',
				'2' => 'Super Destaque',
				'4' => 'Destaque Exclusivo',
				'3' => 'Destaque Superior',
				'5' => 'Destaque Triplo',
			);

			$this->load->model('modosdetrabalhar_model');
			$data['modosdetrabalhar'] = array('' => 'Selecione') + $this->modosdetrabalhar_model->get_modosdetrabalhar_select($this->session->userdata('imobiliaria_id'), 1);

			$result = $this->db->distinct()
				->select('U.id, U.nome')
				->from('imoveis I')
				->join('usuarios U', 'I.corretor_angariador_id = U.id', 'left')
				->where('U.imobiliaria_id', $this->session->userdata('imobiliaria_id'))
				->order_by('U.nome')
				->get()
				->result_array();

			$angariadores = array();
			foreach ($result as $row) {
				$angariadores[$row['id']] = $row['nome'];
			}
			$data['angariadores'] = array('' => 'Selecione') + $angariadores;

			if ($data['galvao']) {
				$result = $this->db->distinct()
					->select('agencia')
					->from('imoveis I')
					->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'))
					->where('agencia IS NOT NULL', null, false)
					->where('agencia <>\'\'', null, false)
					->order_by('agencia')
					->get()
					->result_array();
				$data['agencias'] = array('' => 'Selecione');
				foreach ($result as $row) {
					$data['agencias'][$row['agencia']] = $row['agencia'];
				}

				$result = $this->db->distinct()
					->select('corretor_galvao')
					->from('imoveis I')
					->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left')
					->where('I.imobiliaria_id', $this->session->userdata('imobiliaria_id'))
					->where('corretor_galvao IS NOT NULL', null, false)
					->where('corretor_galvao <>\'\'', null, false)
					->where('M.enviar_para_portais', 1)
					->order_by('corretor_galvao')
					->get()
					->result_array();
				$data['corretores_galvao'] = array('' => 'Selecione');
				foreach ($result as $row) {
					$data['corretores_galvao'][$row['corretor_galvao']] = $row['corretor_galvao'];
				}
			}

			$data['tipos'] = array('' => 'Selecione') + $this->tipos_model->get_tipos_select();

			$this->load->model('imobiliarias_model');
			$data['imobiliaria'] = $this->imobiliarias_model->get_imobiliaria($this->session->userdata('imobiliaria_id'));
			$data['imobiliaria'] = $data['imobiliaria'][0];

			$data['todos_imoveis'] = true;

			$tmp = $this->db->select('id')
				->from('parcerias')
				->where('imobiliaria_id', $data['imobiliaria']['id'])
				->where('enviar_para_portais', 1)
				->where_in('convidada_id', array('355', '1268'))
				->get()
				->result_array();

			$data['mostrar_qtd_imoveis_contratados'] = false;
			if (sizeof($tmp) > 0) {
				$data['mostrar_qtd_imoveis_contratados'] = true;
			}

			$data['qtd_imoveis_contratados'] = $this->db->select('imoveis_permitidos, portal, destaques')
				->from('portais_quantidades')
				->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'))
				->where('portal', $data['campos_busca']['site'])
				->order_by('portal')
				->get()
				->result_array();

			if ($data['campos_busca']['site'] != '') {

				$data['cidades_permitidas'] = array();
				// Caso seja um portal da promentor
				if ($data['campos_busca']['site'] == 'imoveisempinhais' || $data['campos_busca']['site'] == 'imoveisemcuritiba') {
					// Buscar número de imóveis permitidos e imóveis em destaque permitidos
					$dados_portal = $this->db->select('P.cidades_permitidas')
						->from('portais2 P')
						->where('P.nome', $data['campos_busca']['site'])
						->get()
						->row();
					$data['cidades_permitidas'] = $dados_portal->cidades_permitidas;
					$data['cidades_permitidas'] = explode(',', $data['cidades_permitidas']);
				}

				$this->db->select('I.id, I.referencia, T.tipo, B.bairro, C.cidade, I.endereco, I.area_total');
				$this->db->select('I.valor_total, I.aluguel, I.valor_alta_temporada, I.valor_inicial');
				$this->db->select('I.tipo_venda, I.tipo_locacao, I.tipo_lancamento, I.tipo_temporada');
				$this->db->select('I.mostrar_endereco, I.mostrar_numero, I.mostrar_complemento, I.numero');
				$this->db->select('I.complemento, E.edificio, I.area_privativa');
				$this->db->select('IP.site AS selecionado, IP.destacar');
				$this->db->select('num_fotos_imovel(I.id) AS num_fotos');
				$this->db->select('foto_principal(I.id) as foto_principal');
				$this->db->select('M.modo, U.nome as angariador');
				$this->db->select('I.data_do_cadastro');
				$this->db->distinct();
				$this->db->from('imoveis I');
				$this->db->join('cidades C', 'C.id = I.cidade_id', 'left');
				$this->db->join('bairros B', 'B.id = I.bairro_id', 'left');
				$this->db->join('tipos T', 'T.id = I.tipo_id');
				$this->db->join('imobiliarias IM', 'IM.id = I.imobiliaria_id');
				$this->db->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left');
				$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.' . $_POST['pg'] . '=1 AND IP.site=\'' . $data['campos_busca']['site'] . '\'', 'left');
				$this->db->join('edificios E', 'E.id = I.edificio_id', 'left');
				$this->db->join('usuarios U', 'U.id = I.corretor_angariador_id', 'left');
				$this->db->where('M.enviar_para_portais', 1);
				$this->db->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE);
				$this->db->where('IM.ativa', 1);
				$this->db->where($busca);

				if (sizeof($data['cidades_permitidas']) > 0) {
					$this->db->where_in('I.cidade_id', $data['cidades_permitidas']);
				}
				$this->db->order_by('I.dormitorios');

				if (isset($_POST['referencia']) && $_POST['referencia'] != '') {
					$this->db->like('I.referencia', $_POST['referencia']);
					$data['todos_imoveis'] = false;
				}

				if (isset($_POST['endereco']) && $_POST['endereco'] != '') {
					$this->db->like('I.endereco', $_POST['endereco']);
					$data['todos_imoveis'] = false;
				}

				if (isset($_POST['corretor_angariador_id']) && $_POST['corretor_angariador_id'] != '') {
					$this->db->where('I.corretor_angariador_id', $_POST['corretor_angariador_id']);
					$data['todos_imoveis'] = false;
				}

				if (isset($_POST['corretor_galvao']) && $_POST['corretor_galvao'] != '') {
					$this->db->where('I.corretor_galvao', $_POST['corretor_galvao']);
					$data['todos_imoveis'] = false;
				}

				if (isset($_POST['agencia']) && $_POST['agencia'] != '') {
					$this->db->where('I.agencia', $_POST['agencia']);
					$data['todos_imoveis'] = false;
				}

				if (isset($_POST['tipo_id']) && $_POST['tipo_id'] != '') {
					$data['todos_imoveis'] = false;
				}

				if (isset($_POST['modo']) && $_POST['modo'] != '') {
					$data['todos_imoveis'] = false;
				}

				$query = $this->db->get();

				$data['imoveis'] = $query->result_array();

				$this->db->distinct();
				$this->db->select('I.id');
				$this->db->from('imoveis I');
				$this->db->join('cidades C', 'C.id = I.cidade_id', 'left');
				$this->db->join('bairros B', 'B.id = I.bairro_id', 'left');
				$this->db->join('tipos T', 'T.id = I.tipo_id');
				$this->db->join('imobiliarias IM', 'IM.id = I.imobiliaria_id');
				$this->db->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left');
				$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $data['campos_busca']['site'] . '\'', 'left');
				$this->db->where('M.enviar_para_portais', 1);
				$this->db->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE);
				$this->db->where('IM.ativa', 1);
				$this->db->where($busca);
				$this->db->order_by('I.dormitorios');

				$query =  $this->db->get();
				$data['total'] = $query->result_array();
				$data['total'] = sizeof($data['total']);

				$this->db->select('I.id');
				$this->db->distinct();
				$this->db->from('imoveis I');
				$this->db->join('cidades C', 'C.id = I.cidade_id', 'left');
				$this->db->join('bairros B', 'B.id = I.bairro_id', 'left');
				$this->db->join('tipos T', 'T.id = I.tipo_id');
				$this->db->join('imobiliarias IM', 'IM.id = I.imobiliaria_id');
				$this->db->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left');
				$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $data['campos_busca']['site'] . '\'');
				$this->db->where('M.enviar_para_portais', 1);
				$this->db->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE);
				$this->db->where('IM.ativa', 1);
				$this->db->where($busca);
				$this->db->order_by('I.id');

				$query =  $this->db->get();
				$data['selecionados'] = $query->result_array();
				$data['selecionados'] = sizeof($data['selecionados']);

				$this->db->select('I.id');
				$this->db->distinct();
				$this->db->from('imoveis I');
				$this->db->join('cidades C', 'C.id = I.cidade_id', 'left');
				$this->db->join('bairros B', 'B.id = I.bairro_id', 'left');
				$this->db->join('tipos T', 'T.id = I.tipo_id');
				$this->db->join('imobiliarias IM', 'IM.id = I.imobiliaria_id');
				$this->db->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left');
				if (isset($_POST['site']) && ($_POST['site'] == 'zap' || $_POST['site'] == 'mercadolivre')) {
					$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $data['campos_busca']['site'] . '\' AND (IP.destacar=2)');
				} else {
					$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $data['campos_busca']['site'] . '\' AND (IP.destacar=1)');
				}
				$this->db->where('M.enviar_para_portais', 1);
				$this->db->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE);
				$this->db->where('IM.ativa', 1);
				$this->db->where($busca);
				if (isset($busca['I.tipo_venda'])) {
					$this->db->where('IP.tipo_venda', $busca['I.tipo_venda']);
				}
				if (isset($busca['I.tipo_locacao'])) {
					$this->db->where('IP.tipo_locacao', $busca['I.tipo_locacao']);
				}
				$this->db->order_by('I.dormitorios');

				$data['destacados'] =  $this->db->count_all_results();

				$this->db->select('I.id');
				$this->db->distinct();
				$this->db->from('imoveis I');
				$this->db->join('cidades C', 'C.id = I.cidade_id', 'left');
				$this->db->join('bairros B', 'B.id = I.bairro_id', 'left');
				$this->db->join('tipos T', 'T.id = I.tipo_id');
				$this->db->join('imobiliarias IM', 'IM.id = I.imobiliaria_id');
				$this->db->join('modosdetrabalhar M', 'M.id = I.trabalhar_como_id', 'left');
				if (isset($_POST['site']) && ($_POST['site'] == 'zap' || $_POST['site'] == 'mercadolivre')) {
					$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $data['campos_busca']['site'] . '\' AND (IP.destacar=3)');
				} else {
					$this->db->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $data['campos_busca']['site'] . '\' AND (IP.destacar=2)');
				}
				$this->db->where('M.enviar_para_portais', 1);
				$this->db->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE);
				$this->db->where('IM.ativa', 1);
				$this->db->where($busca);
				if (isset($busca['I.tipo_venda'])) {
					$this->db->where('IP.tipo_venda', $busca['I.tipo_venda']);
				}
				if (isset($busca['I.tipo_locacao'])) {
					$this->db->where('IP.tipo_locacao', $busca['I.tipo_locacao']);
				}
				$this->db->order_by('I.dormitorios');

				$data['super_destacados'] =  $this->db->count_all_results();

				if (isset($_POST['site']) && (in_array($_POST['site'], array('vivareal', 'buscacuritiba')))) {

					if (isset($busca['I.tipo_venda'])) {
						$this->db->where('IP.tipo_venda', $busca['I.tipo_venda']);
					}
					if (isset($busca['I.tipo_locacao'])) {
						$this->db->where('IP.tipo_locacao', $busca['I.tipo_locacao']);
					}

					$data['destaque_especial'] =  $this->db->select('I.id')
						->distinct()
						->from('imoveis I')
						->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $_POST['site'] . '\' AND (IP.destacar=3)')
						->join('tipos T', 'T.id = I.tipo_id')
						->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
						->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
						->where('M.enviar_para_portais', 1)
						->where('IM.ativa', 1)
						->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE)
						->where($busca)
						->count_all_results();

					if (isset($busca['I.tipo_venda'])) {
						$this->db->where('IP.tipo_venda', $busca['I.tipo_venda']);
					}
					if (isset($busca['I.tipo_locacao'])) {
						$this->db->where('IP.tipo_locacao', $busca['I.tipo_locacao']);
					}

					$data['destaque_premium'] =  $this->db->select('I.id')
						->distinct()
						->from('imoveis I')
						->join('imoveis_sites_parceiros IP', 'IP.imovel_id = I.id AND IP.site=\'' . $_POST['site'] . '\' AND (IP.destacar=4)')
						->join('tipos T', 'T.id = I.tipo_id')
						->join('imobiliarias IM', 'IM.id = I.imobiliaria_id')
						->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
						->where('M.enviar_para_portais', 1)
						->where('IM.ativa', 1)
						->where('I.referencia IS NOT NULL AND I.imobiliaria_id=' . $this->session->userdata('imobiliaria_id'), '', FALSE)
						->where($busca)
						->count_all_results();
				}

				$this->db->select('ativo');
				$this->db->from('sites_parceiros_selecionar_todos');
				$this->db->where('site', $data['campos_busca']['site']);
				$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
				$this->db->where($_POST['pg'], 1);

				$query = $this->db->get();
				$data['selecionar_todos'] = $query->result_array();

				if (isset($data['selecionar_todos'][0]) && $data['selecionar_todos'][0]['ativo'] == '1') {
					$data['todos_selecionados'] = 1;
					$data['selecionados'] = $data['total'];
				} else {
					$data['todos_selecionados'] = 0;
				}

				$this->db->select('data');
				$this->db->from('log_selecionar_sites_parceiros');
				$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
				$this->db->limit(1);
				$this->db->order_by('id', 'desc');

				$query = $this->db->get();
				$data['data_atualizacao'] = $query->result_array();
				if (isset($data['data_atualizacao'][0])) {
					$data['data_atualizacao'] = $data['data_atualizacao'][0];
				} else {
					$data['data_atualizacao']['data'] = '';
				}

				// Caso seja um portal, no momento só tem o imoveisempinhais
				if ($data['campos_busca']['site'] == 'imoveisempinhais') {
					// Buscar número de imóveis permitidos e imóveis em destaque permitidos
					$dados_portal = $this->db->select('PQ.imoveis_permitidos')
						->select('P.imoveis_permitidos AS imoveis_permitidos_padrao, PQ.destaques')
						->select('P.numero_destaques AS destaques_padrao')
						->from('portais2 P')
						->join('portais_quantidades PQ', 'PQ.portal = P.nome AND PQ.imobiliaria_id=' . $data['imobiliaria']['id'], 'left')
						->where('P.nome', $data['campos_busca']['site'])
						->get()
						->row();
					$data['numero_imoveis_permitidos'] = ($dados_portal->imoveis_permitidos != '') ? $dados_portal->imoveis_permitidos : $dados_portal->imoveis_permitidos_padrao;
					$data['numero_destaques_permitidos'] = ($dados_portal->destaques != '') ? $dados_portal->destaques : $dados_portal->destaques_padrao;

					if ($data['selecionados'] > $data['numero_imoveis_permitidos']) {
						// Remover alguns imóveis selecionados até chegar ao limite
						$qtd_deletar = $data['selecionados'] - $data['numero_imoveis_permitidos'];
						$tmp = $this->db->where('site', $data['campos_busca']['site'])
							->where('imobiliaria_id', $data['imobiliaria']['id'])
							->where($_POST['pg'], 1)
							->limit($qtd_deletar)
							->delete('imoveis_sites_parceiros');

						$data['selecionados'] = $data['numero_imoveis_permitidos'];
					}

					if ($data['destacados'] > $data['numero_destaques_permitidos']) {
						// Remover alguns imóveis selecionados até chegar ao limite
						$qtd_deletar = $data['destacados'] - $data['numero_destaques_permitidos'];
						$tmp = $this->db->set('destacar', 0)
							->where('site', $data['campos_busca']['site'])
							->where('imobiliaria_id', $data['imobiliaria']['id'])
							->where($_POST['pg'], 1)
							->where('destacar', 1)
							->limit($qtd_deletar)
							->update('imoveis_sites_parceiros');

						$data['destacados'] = $data['numero_destaques_permitidos'];
					}
				}
			} else {
				$data['todos_selecionados'] = 0;
				$data['imoveis'] = array();
				$data['total'] = '';
				$data['destacados'] = '';
				$data['selecionados'] = '';
				$data['data_atualizacao']['data'] = '';
			}

			if (isset($_GET['format']) && $_GET['format'] == 'excel') {
				$result = array();
				if ($data['todos_selecionados'] == 1) {
					$result = $data['imoveis'];
				} else {
					foreach ($data['imoveis'] as $imovel) {

						if ($imovel['selecionado']) {
							$imovel['portal'] = $_POST['site'];
							$imovel['transacao'] = ($imovel['tipo_locacao'] == 1) ? 'locação' : 'venda';

							if ($imovel['transacao'] == 'venda') {
								$imovel['valor'] = ($imovel['valor_total']) ? number_format($imovel['valor_total'], 2, ',', '.') : '';
							} else {
								$imovel['valor'] = ($imovel['aluguel']) ? number_format($imovel['aluguel'], 2, ',', '.') : '';
							}

							if (isset($imovel['data_do_cadastro']) && $imovel['data_do_cadastro'] != '') $imovel['data_do_cadastro'] = date("d/m/Y", strtotime($imovel['data_do_cadastro']));

							$imovel['destaque'] = '';
							if (isset($imovel['destacar'])) {

								if ($data['campos_busca']['site'] == 'imovelweb' || $data['campos_busca']['site'] == 'casamineira') {
									$imovel['destaque'] = $data['tipos_destaque_imovelweb'][$imovel['destacar']];
								} elseif ($data['campos_busca']['site'] == 'zap') {
									$imovel['destaque'] = $data['tipos_destaque2'][$imovel['destacar']];
								} elseif ($data['campos_busca']['site'] == 'mercadolivre') {
									$imovel['destaque'] = $data['tipos_destaque_mercadolivre'][$imovel['destacar']];
								} elseif (in_array($data['campos_busca']['site'], array('vivareal', 'buscacuritiba'))) {
									$imovel['destaque'] = $data['tipos_destaque3'][$imovel['destacar']];
								} elseif (
									$data['campos_busca']['site'] == 'imoviewtv' ||
									$data['campos_busca']['site'] == 'rodadadeimoveis' ||
									$data['campos_busca']['site'] == 'attria' ||
									$data['campos_busca']['site'] == 'facebook' ||
									$data['campos_busca']['site'] == 'creci'
								) {
								} else if ($data['campos_busca']['site'] != 'redeurbana') {
									$imovel['destaque'] = 'destacado';
								}
							}

							array_push($result, $imovel);
						}
					}
				}

				var_dump($result);
				exit();
			}

			$this->load->view('admin/exportacao/selecionar', $data);
		}
	}

	public function gravar_log()
	{
		$dados = array(
			'imobiliaria_id' => $this->session->userdata('imobiliaria_id')
		);
		$this->db->insert('log_selecionar_sites_parceiros', $dados);
	}
}
