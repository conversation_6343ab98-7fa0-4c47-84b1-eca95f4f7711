<?php

class Imovelparceiro extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('usuarios_model');
		$this->usuarios_model->verify_login();
		$this->load->model('imoveis_model');
		$this->load->model('cidades_model');
		$this->load->model('bairros_model');
		$this->load->model('tipos_model');
		$this->load->model('parcerias_model');
		$this->load->model('mongodb_model');
		$this->load->library('session');
		$this->load->helper(array('adminforms'));

		if ($this->session->userdata('permissao_alterar') != 1) redirect('admin/main');

		$informacoes = array(
			'pagina' => 'imovelparceiro',
			'titulo' => 'Selecionar imóveis de parceiros',
			'filtragem' => 'Todos os imóveis',
			'validacao' => '',
			'abreviacao_banco' => 'IP',
			'tab' => ''
		);

		$config = array(
			'informacoes' => $informacoes
		);

		$this->data = $config;
	}

	/**
	 * Adicionar imóvel na listagem
	 */
	public function adicionar()
	{
		$data['ci'] = $this;
		$parceira = $_POST['imob'];
		$pg = $_POST['pg'];
		$imovel_id = $_POST['id'];
		$referencia = $_POST['referencia'];

		// Com os imóveis da caixa a busca deve ser sempre por referência e não id
		if ($parceira == '1268') {
			$this->db->where('referencia', $referencia);
		} else {
			$this->db->where('imovel_id', $imovel_id);
		}

		$this->db->select('id')
			->from('imoveis_selecionados_parceiros')
			->where('parceira', $parceira)
			->where($pg, 1)
			->where('imobiliaria_id', $this->session->userdata['imobiliaria_id']);
		$query = $this->db->get();
		$imovel_encontrado = $query->result_array();

		if (sizeof($imovel_encontrado) > 0) {
			// imóvel já existe no banco;
		} else {
			$this->db->set('imobiliaria_id', $this->session->userdata['imobiliaria_id'])
				->set('parceira', $parceira)
				->set('imovel_id', $imovel_id)
				->set('referencia', $referencia)
				->set($pg, 1)
				->insert('imoveis_selecionados_parceiros');

			$imobiliaria = $this->db->select('mongo_host')
				->from('imobiliarias')
				->where('id', $this->session->userdata['imobiliaria_id'])
				->get()
				->result_array();
			$imobiliaria  = $imobiliaria[0];

			$host_mongo = $imobiliaria['mongo_host'];
			if (isset($host_mongo) && $host_mongo != '') {
				$this->session->set_userdata('mongo_host', $host_mongo);
				$this->load->model('mongodb_model');
				$this->mongodb_model->cadastrarImovel($imovel_id);
			}
		}
	}

	/**
	 * Adicionar vários imóveis de uma vez só na listagem
	 */
	public function adicionar_batch()
	{
		$data['ci'] = $this;
		$parceira = $_POST['imob'];
		$pg = $_POST['pg'];
		$tmp = $_POST['id'];
		$tmp2 = $_POST['referencia'];
		$imoveis = explode(',', $tmp);
		$referencias = explode(',', $tmp2);

		// Deletar os já cadastrados, para evitar duplicações
		$this->remover_batch();

		// Criar uma array com todos os imóveis a serem inseridos para fazer apenas um insert
		$array = [];
		for ($i = 0; $i < sizeof($imoveis); $i++) {
			array_push($array, array(
				'imobiliaria_id' => $this->session->userdata['imobiliaria_id'],
				'parceira' => $parceira,
				'imovel_id' => $imoveis[$i],
				'referencia' => $referencias[$i],
				$pg => 1
			));
		}

		$this->db->insert_batch('imoveis_selecionados_parceiros', $array);

		// Atualização do mongodb

		// Busca o host do mongo
		$imobiliaria = $this->db->select('mongo_host')
			->from('imobiliarias')
			->where('id', $this->session->userdata['imobiliaria_id'])
			->get()
			->result_array();
		$imobiliaria  = $imobiliaria[0];
		$host_mongo = $imobiliaria['mongo_host'];

		if (isset($host_mongo) && $host_mongo != '') {
			$this->session->set_userdata('mongo_host', $host_mongo);
			$this->load->model('mongodb_model');

			// Cadastrar cada novo imóvel individualmente pois na maioria das vezes será mais rápido que 
			// atualizar o banco todo. Tem vários casos de imobiliárias com mais de 1000 imóveis em que 
			// o mongo demoraria pra atualizar
			for ($i = 0; $i < sizeof($imoveis); $i++) {
				$this->mongodb_model->cadastrarImovel($imoveis[$i]);
			}
		}

		echo 'OK';
	}

	/**
	 * Remover imóvel da listagem
	 */
	public function remover()
	{
		$data['ci'] = $this;
		$parceira = $_POST['imob'];
		$pg = $_POST['pg'];
		$imovel_id = $_POST['id'];
		$referencia = $_POST['referencia'];

		// Com os imóveis da caixa a busca deve ser sempre por referência e não id
		if ($parceira == '1268') {
			$this->db->where('referencia', $referencia);
		} else {
			$this->db->where('imovel_id', $imovel_id);
		}

		$this->db->from('imoveis_selecionados_parceiros')
			->where('parceira', $parceira)
			->where($pg, 1)
			->where('imobiliaria_id', $this->session->userdata['imobiliaria_id'])
			->delete();

		$this->db->set('ativo', 0)
			->where('parceira', $parceira)
			->where($pg, 1)
			->where('imobiliaria_id', $this->session->userdata['imobiliaria_id'])
			->update('imoveis_parceiros_selecionar_todos');

		$imobiliaria = $this->db->select('mongo_host')
			->from('imobiliarias')
			->where('id', $this->session->userdata['imobiliaria_id'])
			->get()
			->result_array();
		$imobiliaria  = $imobiliaria[0];

		$host_mongo = $imobiliaria['mongo_host'];
		if (isset($host_mongo) && $host_mongo != '') {
			$this->session->set_userdata('mongo_host', $host_mongo);
			$this->load->model('mongodb_model');
			if ($parceira == '1268') { // Imóveis da caixa
				$this->mongodb_model->deletarImovelPorRef($referencia);
			} else {
				$this->mongodb_model->deletarImovel($imovel_id);
			}
		}
	}

	/**
	 * Remover vários imóveis de uma vez só na listagem
	 */
	public function remover_batch()
	{
		$data['ci'] = $this;
		$parceira = $_POST['imob'];
		$pg = $_POST['pg'];
		$tmp = $_POST['id'];
		$tmp2 = $_POST['referencia'];
		$imoveis = explode(',', $tmp);
		$referencias = explode(',', $tmp2);

		// Com os imóveis da caixa a busca deve ser sempre por referência e não id
		if ($parceira == '1268') {
			$this->db->where_in('referencia', $referencias);
		} else {
			$this->db->where_in('imovel_id', $imoveis);
		}

		// Deletar os já cadastrados, para evitar duplicações
		$this->db->from('imoveis_selecionados_parceiros')
			->where('parceira', $parceira)
			->where($pg, 1)
			->where('imobiliaria_id', $this->session->userdata['imobiliaria_id'])
			->delete();

		$imobiliaria = $this->db->select('mongo_host')
			->from('imobiliarias')
			->where('id', $this->session->userdata['imobiliaria_id'])
			->get()
			->result_array();
		$imobiliaria  = $imobiliaria[0];

		$host_mongo = $imobiliaria['mongo_host'];
		if (isset($host_mongo) && $host_mongo != '') {
			$this->session->set_userdata('mongo_host', $host_mongo);
			$this->load->model('mongodb_model');

			for ($i = 0; $i < sizeof($imoveis); $i++) {
				// Passar o false pra não atualizar os tipos, atualizar os tipos apenas depois de deletar tudo
				$this->mongodb_model->deletarImovel($imoveis[$i], false);
			}
			$this->mongodb_model->inicializarTipos();
		}

		echo 'OK';
	}

	/**
	 * Retorna o número de imóveis selecionados
	 */
	public function selecionados($parceira_id, $pg = null)
	{
		$this->load->model('parcerias_model', 'parcerias');
		echo $this->parcerias->get_num_selecionados($parceira_id, ($pg) ? $pg : null);
	}

	/**
	 * Página inicial, busca os imóveis para seleção
	 */
	public function selecionar()
	{
		$data['ci'] = $this;
		$data['informacoes'] = $this->data['informacoes'];
		$data['pagina'] = $data['informacoes']['pagina'];

		$data['pgs'] = array(
			'' => 'Selecione',
			'tipo_venda' => 'Venda',
			'tipo_locacao' => 'Locação',
			'tipo_lancamento' => 'Lançamento',
			'tipo_temporada' => 'Temporada'
		);

		$data['estados'] = array('' => 'Todos') + getEstadosSelect();

		$data['opcoes_selecionados'] = array(
			'' => 'Ver todos',
			'1' => 'Somente selecionados',
			'0' => 'Não selecionados'
		);

		if (isset($_POST['estado']) && $_POST['estado']) {
			$data['cidades'] = array('' => 'Todas') + $this->cidades_model->get_cidades_select($_POST['estado'], true);
			$data['bairros'] = array('' => 'Todos') + $this->bairros_model->get_bairros_select((isset($_POST['cidade_id'])) ? $_POST['cidade_id'] : $this->session->userdata['imobiliaria_cidade_id']);
		} else {
			$estado = ($this->session->userdata['imobiliaria_estado']) ? $this->session->userdata['imobiliaria_estado'] : 'PR';

			if (isset($_GET['parceiro']) && $_GET['parceiro'] == 1268) {
				$data['cidades'] = array('' => 'Todas');
				$data['bairros'] = array('' => 'Todos');
			} else {
				$data['cidades'] = array('' => 'Todas') + $this->cidades_model->get_cidades_select($estado, true);
				$data['bairros'] = array('' => 'Todos') + $this->bairros_model->get_bairros_select((isset($_POST['cidade_id'])) ? $_POST['cidade_id'] : $this->session->userdata['imobiliaria_cidade_id']);
			}
		}

		if (isset($_POST['parceiro_id'])) {
			$tipos = $this->tipos($_POST['parceiro_id']);
			$tipos = json_decode($tipos);
			$data['tipos'] = array('' => 'Selecione') + (array)$tipos;

			$_GET['parceiro'] = $_POST['parceiro_id'];
		} else {
			$data['tipos'] = array('' => 'Selecione') + $this->tipos_model->get_tipos_select();
		}

		if (isset($_GET['parceiro'])) {
			$result = $this->db
				->select('estagio_da_obra')
				->from('imoveis')
				->where('imobiliaria_id', $_GET['parceiro'])
				->order_by('estagio_da_obra')
				->distinct()
				->get();
			$data['estagios_da_obra'] = array();
			if ($result->num_rows() > 0) {
				foreach ($result->result_array() as $row) {
					$data['estagios_da_obra'][$row['estagio_da_obra']] = $row['estagio_da_obra'];
				}
			}
			$data['estagios_da_obra'] = array('' => 'Selecione') + $data['estagios_da_obra'];

			$result = $this->db
				->select('tipo_de_uso')
				->from('imoveis')
				->where('imobiliaria_id', $_GET['parceiro'])
				->order_by('tipo_de_uso')
				->distinct()
				->get();
			if ($result->num_rows() > 0) {
				foreach ($result->result_array() as $row) {
					$data['tipos_de_uso'][$row['tipo_de_uso']] = $row['tipo_de_uso'];
				}
			} else {
				$data['tipos_de_uso'] = array();
			}
			$data['tipos_de_uso'] = array('' => 'Selecione') + $data['tipos_de_uso'];

			$result = $this->db
				->select('tipo_construcao')
				->from('imoveis')
				->where('imobiliaria_id', $_GET['parceiro'])
				->order_by('tipo_construcao')
				->distinct()
				->get();
			if ($result->num_rows() > 0) {
				foreach ($result->result_array() as $row) {
					$data['tipos_construcao'][$row['tipo_construcao']] = $row['tipo_construcao'];
				}
			} else {
				$data['tipos_construcao'] = array();
			}
			$data['tipos_construcao'] = array('' => 'Selecione') + $data['tipos_construcao'];
		} else {
			$data['estagios_da_obra'] = array();
			$data['tipos_de_uso'] = array();
			$data['tipos_construcao'] = array();
		}

		$data['parceiros'] = array('' => 'Selecione') + $this->parcerias_model->get_parcerias_select();

		if (isset($_POST['parceiro_id'])) {
			$this->db->_protect_identifiers = FALSE;
			$this->db->select('I.id, I.referencia, T.tipo, T.id as tipo_id, 
				B.bairro, I.endereco, I.valor_total, I.aluguel, I.tipo_venda, 
				I.tipo_locacao, I.area_total, ISP.imovel_id as parceira, 
				I.observacoes, C.cidade, I2.orulo_id, E.edificio, M.modo,
				I.area_privativa, U.nome AS angariador, I.data_do_cadastro')
				->select('num_fotos_imovel(I.id) AS num_fotos', FALSE)
				->distinct()
				->from('imoveis I')
				->join('tipos T', 'T.id = I.tipo_id')
				->join('parcerias_tipos PT', 'I.tipo_id = PT.tipo_parceira ', 'left')
				->join('cidades C', 'C.id = I.cidade_id', 'left')
				->join('bairros B', 'B.id = I.bairro_id', 'left')
				->join('edificios E', 'E.id = I.edificio_id', 'left')
				->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
				->join('usuarios U', 'U.id = I.corretor_angariador_id', 'left')
				->where('I.imobiliaria_id', $_POST['parceiro_id'])
				->where('M.mostrar', 1)
				->where(' (I.imob_codigo_imoview <> ' . $this->session->userdata['imobiliaria_id'] . ' OR I.imob_codigo_imoview IS NULL)', false, false)
				->order_by('I.referencia');

			// Com os imóveis da caixa a busca deve ser sempre por referência e não id
			if ($_POST['parceiro_id'] == '1268') {
				$this->db->join('imoveis_selecionados_parceiros ISP', 'ISP.imobiliaria_id = ' . $this->session->userdata['imobiliaria_id'] . ' AND ISP.parceira = I.imobiliaria_id AND ISP.referencia = I.referencia', 'left');
			} else {
				$this->db->join('imoveis_selecionados_parceiros ISP', 'ISP.imobiliaria_id = ' . $this->session->userdata['imobiliaria_id'] . ' AND ISP.parceira = I.imobiliaria_id AND ISP.imovel_id = I.id', 'left');
			}

			if ($_POST['pg']) $this->db->where('I.' . $_POST['pg'], 1);
			if ($_POST['referencia']) $this->db->like('I.referencia', $_POST['referencia']);
			if ($_POST['tipo_id']) $this->db->where('PT.tipo_ligacao', $_POST['tipo_id']);
			if ($_POST['endereco']) $this->db->like('I.endereco', $_POST['endereco']);
			if ($_POST['edificio']) $this->db->like('E.edificio', $_POST['edificio']);
			if ($_POST['estado']) $this->db->where('I.estado', $_POST['estado']);
			if ($_POST['cidade_id']) $this->db->where('I.cidade_id', $_POST['cidade_id']);
			if ($_POST['bairro_id']) $this->db->where('I.bairro_id', $_POST['bairro_id']);
			if (isset($_POST['estagio_da_obra']) && $_POST['estagio_da_obra']) $this->db->where('I.estagio_da_obra', $_POST['estagio_da_obra']);
			if (isset($_POST['tipo_de_uso']) && $_POST['tipo_de_uso']) $this->db->where('I.tipo_de_uso', $_POST['tipo_de_uso']);
			if (isset($_POST['tipo_construcao']) && $_POST['tipo_construcao']) $this->db->where('I.tipo_construcao', $_POST['tipo_construcao']);
			if ($_POST['construtora']) $this->db->like('I.construtora', $_POST['construtora']);
			if ($_POST['situacao'] == '1') {
				$this->db->where("ISP.imovel_id <>''", false, false);
			} else if ($_POST['situacao'] == '0') {
				$this->db->where(" (ISP.imovel_id IS NULL or ISP.imovel_id='')", false, false);
			} else if ($_POST['situacao'] == '2') {
				$this->db->where(" (ISP.imovel_id IS NULL or ISP.imovel_id='')", false, false);
			}

			if ($_POST['valor_de'] != '') $this->db->where('I.valor_total >=', str_replace(',', '.', str_replace('.', '', $_POST['valor_de'])));
			if ($_POST['valor_ate'] != '') $this->db->where('I.valor_total <=', str_replace(',', '.', str_replace('.', '', $_POST['valor_ate'])));

			if ($_POST['area_privativa_de'] != '') $this->db->where('I.area_privativa >=', str_replace(',', '.', str_replace('.', '', $_POST['area_privativa_de'])));
			if ($_POST['area_privativa_ate'] != '') $this->db->where('I.area_privativa <=', str_replace(',', '.', str_replace('.', '', $_POST['area_privativa_ate'])));

			if ($_POST['area_total_de'] != '') $this->db->where('I.area_total >=', str_replace(',', '.', str_replace('.', '', $_POST['area_total_de'])));
			if ($_POST['area_total_ate'] != '') $this->db->where('I.area_total <=', str_replace(',', '.', str_replace('.', '', $_POST['area_total_ate'])));

			if ($_POST['area_terreno_de'] != '') $this->db->where('I.area_terreno >=', str_replace(',', '.', str_replace('.', '', $_POST['area_terreno_de'])));
			if ($_POST['area_terreno_ate'] != '') $this->db->where('I.area_terreno <=', str_replace(',', '.', str_replace('.', '', $_POST['area_terreno_ate'])));

			if (isset($_POST['modalidade']) && $_POST['modalidade']) {
				$where = "JSON_EXTRACT(I.outros, '$.modalidade') = '{$_POST['modalidade']}' ";
				$this->db->where($where);
			}
			if (isset($_POST['situacao2']) && $_POST['situacao2']) {
				$where = "JSON_EXTRACT(I.outros, '$.situacao') = '{$_POST['situacao']}' ";
				$this->db->where($where);
			}
			if (isset($_POST['financiamento']) && $_POST['financiamento'] != '') {
				$where = "JSON_EXTRACT(I.outros, '$.financiamento') = {$_POST['financiamento']} ";
				$this->db->where($where);
			}
			if (isset($_POST['fgts']) && $_POST['fgts'] != '') {
				$where = "JSON_EXTRACT(I.outros, '$.fgts') = {$_POST['fgts']} ";
				$this->db->where($where);
			}
			if (isset($_POST['parcelamento']) && $_POST['parcelamento'] != '') {
				$where = "JSON_EXTRACT(I.outros, '$.parcelamento') = {$_POST['parcelamento']} ";
				$this->db->where($where);
			}
			if (isset($_POST['consorcio']) && $_POST['consorcio'] != '') {
				$where = "JSON_EXTRACT(I.outros, '$.consorcio') = {$_POST['consorcio']} ";
				$this->db->where($where);
			}
			if (isset($_POST['descricao']) && $_POST['descricao'] != '') {
				$where = "JSON_UNQUOTE(JSON_EXTRACT(I.outros, '$.descricao_original')) COLLATE utf8mb4_general_ci LIKE '%{$_POST['descricao']}%' ";
				$this->db->where($where);
			}

			if ($_POST['comissao'] && $_POST['comissao_operador']) {
				if ($_POST['comissao'] != '' && $_POST['comissao_operador'] != '') {
					$_POST['comissao'] = str_replace(',', '.', $_POST['comissao']);
					$this->db->where("I.comissao " . $_POST['comissao_operador'] . " " . $_POST['comissao'], false, false);
				}
			}

			if (isset($_POST['desconto_calculado']) && $_POST['desconto_calculado'] && $_POST['desconto_operador']) {
				if ($_POST['desconto_calculado'] != '' && $_POST['desconto_operador'] != '') {
					$_POST['desconto_calculado'] = str_replace(',', '.', $_POST['desconto_calculado']);
					$this->db->where("I.desconto_calculado " . $_POST['desconto_operador'] . " " . $_POST['desconto_calculado'], false, false);
				}
			}

			if ($_POST['situacao'] == '2') {
				$this->db->join('imoveis I2', 'I2.orulo_id = I.id AND I2.imobiliaria_id=' . $this->session->userdata['imobiliaria_id']);
			} else {
				$this->db->join('imoveis I2', 'I2.orulo_id = I.id AND I2.imobiliaria_id=' . $this->session->userdata['imobiliaria_id'], 'left');
			}

			if ($_POST['data_atualizacao']) $this->db->where("I.alteracao >='" . tratar_valor_para_bd($_POST['data_atualizacao'], 'data') . " 00:00'", false, false);
			$query = $this->db->get();
			$data['imoveis'] = $query->result_array();
			$this->db->_protect_identifiers = TRUE;

			$data['total'] = sizeof($data['imoveis']);

			$this->load->model('parcerias_model', 'parcerias');
			$data['selecionados'] =  $this->parcerias->get_num_selecionados($_POST['parceiro_id'], ($_POST['pg']) ? $_POST['pg'] : null);

			$this->db->select('ativo')
				->from('imoveis_parceiros_selecionar_todos')
				->where('parceira',  $_POST['parceiro_id'])
				->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'))
				->where('ativo', 1)
				->where($_POST['pg'], 1);
			$query = $this->db->get();
			$data['selecionar_todos'] = $query->result_array();
			if (sizeof($data['selecionar_todos']) > 0) {
				$data['selecionar_todos'] = $data['selecionar_todos'][0]['ativo'];

				for ($i = 0; $i < sizeof($data['imoveis']); $i++) {
					$data['imoveis'][$i]['parceira'] = $data['imoveis'][$i]['id'];
				}
				$data['selecionados'] = sizeof($data['imoveis']);
			} else {
				$data['selecionar_todos'] = 0;
			}
		} elseif (isset($_GET['parceiro'])) {
			$this->db->_protect_identifiers = FALSE;
			$this->db->select('I.id, I.referencia, T.tipo, T.id as tipo_id, 
				B.bairro, I.endereco, I.valor_total, I.aluguel, I.tipo_venda, 
				I.tipo_locacao, I.area_total, ISP.imovel_id as parceira, 
				I.observacoes, C.cidade, E.edificio')
				->distinct()
				->from('imoveis I')
				->join('tipos T', 'T.id = I.tipo_id')
				->join('parcerias_tipos PT', 'I.tipo_id = PT.tipo_parceira ', 'left')
				->join('cidades C', 'C.id = I.cidade_id', 'left')
				->join('bairros B', 'B.id = I.bairro_id', 'left')
				->join('edificios E', 'E.id = I.edificio_id', 'left')
				->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
				->where('I.imobiliaria_id', $_GET['parceiro'])
				->where(' (I.imob_codigo_imoview <> ' . $this->session->userdata['imobiliaria_id'] . ' OR I.imob_codigo_imoview IS NULL)', false, false)
				->where('M.mostrar', 1);
			// Com os imóveis da caixa a busca deve ser sempre por referência e não id
			if ($_GET['parceiro'] == '1268') {
				$this->db->join('imoveis_selecionados_parceiros ISP', 'ISP.imobiliaria_id = ' . $this->session->userdata['imobiliaria_id'] . ' AND ISP.parceira = I.imobiliaria_id AND ISP.referencia = I.referencia', 'left');
			} else {
				$this->db->join('imoveis_selecionados_parceiros ISP', 'ISP.imobiliaria_id = ' . $this->session->userdata['imobiliaria_id'] . ' AND ISP.parceira = I.imobiliaria_id AND ISP.imovel_id = I.id', 'left');
			}

			$query = $this->db->get();
			$tmp = $query->result_array();
			$data['total'] = sizeof($tmp);

			$this->db->select('I.id, I.referencia, T.tipo, T.id as tipo_id, 
				B.bairro, I.endereco, I.valor_total, I.aluguel, I.tipo_venda, 
				I.tipo_locacao, I.area_total, ISP.imovel_id as parceira, 
				I.observacoes, C.cidade')
				->distinct()
				->from('imoveis I')
				->join('tipos T', 'T.id = I.tipo_id')
				->join('parcerias_tipos PT', 'I.tipo_id = PT.tipo_parceira ', 'left')
				->join('cidades C', 'C.id = I.cidade_id', 'left')
				->join('bairros B', 'B.id = I.bairro_id', 'left')
				->join('modosdetrabalhar M', 'I.trabalhar_como_id = M.id')
				->where('I.imobiliaria_id', $_GET['parceiro'])
				->where('M.mostrar', 1);
			// Com os imóveis da caixa a busca deve ser sempre por referência e não id
			if ($_GET['parceiro'] == '1268') {
				$this->db->join('imoveis_selecionados_parceiros ISP', 'ISP.imobiliaria_id = ' . $this->session->userdata['imobiliaria_id'] . ' AND ISP.parceira = I.imobiliaria_id AND ISP.referencia = I.referencia');
			} else {
				$this->db->join('imoveis_selecionados_parceiros ISP', 'ISP.imobiliaria_id = ' . $this->session->userdata['imobiliaria_id'] . ' AND ISP.parceira = I.imobiliaria_id AND ISP.imovel_id = I.id');
			}
			$query = $this->db->get();
			$tmp = $query->result_array();
			$data['selecionados'] = sizeof($tmp);

			$this->db->select('ativo')
				->from('imoveis_parceiros_selecionar_todos')
				->where('parceira',  $_GET['parceiro'])
				->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'))
				->where('ativo', 1);
			$query = $this->db->get();
			$data['selecionar_todos'] = $query->result_array();
			if (sizeof($data['selecionar_todos']) > 0) {
				$data['selecionar_todos'] = $data['selecionar_todos'][0]['ativo'];
				$data['selecionados'] = $data['total'];
			} else {
				$data['selecionar_todos'] = 0;
			}

			$this->db->_protect_identifiers = TRUE;
		} else {
			$data['total'] = 0;
			$data['selecionados'] = 0;
		}

		if (isset($_GET['format']) && $_GET['format'] == 'excel') {
			$result = array();
			if ($data['selecionar_todos'] == 1) {
				$result = $data['imoveis'];
			} else {
				foreach ($data['imoveis'] as $imovel) {
					if ($imovel['parceira'] == $imovel['id']) {
						if (isset($imovel['data_do_cadastro']) && $imovel['data_do_cadastro'] != '') $imovel['data_do_cadastro'] = date("d/m/Y", strtotime($imovel['data_do_cadastro']));
						$imovel['transacao'] = ($imovel['tipo_locacao'] == 1) ? 'locação' : 'venda';
						array_push($result, $imovel);
					}
				}
			}
		}

		$this->load->view('admin/imovelparceiro/selecionar', $data);
	}

	/**
	 * Página para selecionar ou remover todos os imóveis
	 */
	public function selecionar_todos()
	{
		$data['ci'] = $this;
		$opcao = $_POST['o'];
		$parceira = $_POST['imob'];
		$pg = $_POST['pg'];

		$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
		$this->db->where('parceira', $parceira);
		$this->db->where($pg, 1);
		$this->db->delete('imoveis_selecionados_parceiros');

		if ($opcao == 'adicionar') {
			$imoveis = explode(',', $_POST['ids']);
			$data = array();
			$x = 0;
			foreach ($imoveis as $imovel) {
				$data[$x]['imovel_id'] = $imovel;
				$data[$x]['imobiliaria_id'] = $this->session->userdata('imobiliaria_id');
				$data[$x]['parceira'] = $parceira;
				$data[$x][$pg] = 1;
				$x++;
			}
			$this->db->insert_batch('imoveis_selecionados_parceiros', $data);

			$this->db->where('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
			$this->db->where('parceira', $parceira);
			$this->db->where($pg, 1);
			$this->db->delete('imoveis_parceiros_selecionar_todos');

			$this->db->set('imobiliaria_id', $this->session->userdata('imobiliaria_id'));
			$this->db->set('parceira', $parceira);
			$this->db->set('ativo', 1);
			$this->db->set($pg, 1);
			$this->db->insert('imoveis_parceiros_selecionar_todos');
		} else {
			$this->db->set('ativo', 0)
				->where('parceira', $parceira)
				->where($pg, 1)
				->where('imobiliaria_id', $this->session->userdata['imobiliaria_id'])
				->update('imoveis_parceiros_selecionar_todos');
		}

		$imobiliaria = $this->db->select('mongo_host')
			->from('imobiliarias')
			->where('id', $this->session->userdata['imobiliaria_id'])
			->get()
			->result_array();
		$imobiliaria  = $imobiliaria[0];

		$host_mongo = $imobiliaria['mongo_host'];
		if (isset($host_mongo) && $host_mongo != '') {
			$this->session->set_userdata('mongo_host', $host_mongo);
			$this->load->model('mongodb_model');
			$this->mongodb_model->inicializarTipos();
			$this->mongodb_model->inicializarImoveis();
			$this->mongodb_model->inicializarDestaques();
		}
	}

	/**
	 * Retornar os tipos das imobiliarias parceiras em json
	 */
	public function tipos($imobiliaria_parceira)
	{
		$data['ci'] = $this;
		$this->db->select('PT.tipo_ligacao AS id, T.tipo', false, false);
		$this->db->where('I.imobiliaria_id', $imobiliaria_parceira);
		$this->db->join('imoveis I', 'I.tipo_id = T.id');
		$this->db->join('parcerias_tipos PT', 'I.tipo_id = PT.tipo_parceira  AND PT.imobiliaria_id=' . $this->session->userdata['imobiliaria_id'], 'left');
		$this->db->where(' (PT.tipo_ligacao is not null OR T.imobiliaria_id=' . $imobiliaria_parceira . ')', false, false);
		$this->db->from('tipos T');
		$this->db->order_by('T.tipo');
		$this->db->distinct();
		$result = $this->db->get();

		$tipos = array();
		if ($result->num_rows() > 0) {
			foreach ($result->result_array() as $row) {
				$tipos[$row['id']] = $row['tipo'];
			}
		}

		if (isset($_GET['r']) && $_GET['r'] == 'json') {
			$this->output
				->set_content_type('application/json')
				->set_output(json_encode($tipos));
		} else {
			return json_encode($tipos);
		}
	}
}
