<?php
$ci->load->view('admin/template/header');
$ci->load->view('admin/template/menu');

if ($destacados > $selecionados) {
	//$destacados = $selecionados;
}
?>

<div class="loading"></div>

<div class="container">
	<div class="conteudo_padrao conteudo show-grid">
		<h1 class="titulo">Selecionar imóveis para Portais</h1>
		<div class="botoes_cadastro" style="margin-top: -45px;">
			<div class="btn-group botoes-exportar">
				<a class="btn btn-inverse btn-excel" data-original-title="Exportar para excel" data-placement="top" rel="tooltip"><i class="icon-csv icon-white"></i></a>
			</div>
		</div>

		<form id="buscar-relatorio" method="post" action="<?= site_url() ?>/admin/exportacao/selecionar">
			<h2><PERSON><PERSON><PERSON></h2>
			<div class="box-principal card">
				<div class="site-parceiro">
					<div class="input-checkbox">
						<label>Site Parceiro</label>
						<?= form_dropdown('site', $sites, $campos_busca['site'], 'id="site_parceiro"'); ?>
					</div>

					<div>
						<?php
						$com_super_destaques = in_array($campos_busca['site'], array('casamineira', 'imovelweb', 'vivareal', 'zap', 'buscacuritiba'));
						$com_destaque_especial = in_array($campos_busca['site'], array('vivareal', 'buscacuritiba'));
						$com_destaque_premium = in_array($campos_busca['site'], array('vivareal', 'buscacuritiba'));
						?>
						Total de imóveis na Carteira: <?= $total ?><br>
						Total de imóveis Selecionados: <span id="selecionados"><?= $selecionados ?></span><br>
						<?php if ($campos_busca['site'] == 'imoveisempinhais') { ?>
							Total de imóveis Permitidos no portal: <?= $numero_imoveis_permitidos ?></span><br>
						<?php } ?>

						<?php if ($campos_busca['site'] == 'mercadolivre') { ?>
							Total de imóveis Gold: <span id="destacados"><?= $destacados ?></span><br>
						<?php } else { ?>
							Total de imóveis Destacados: <span id="destacados"><?= $destacados ?></span><br>
						<?php } ?>

						<?php if ($campos_busca['site'] == 'imoveisempinhais') { ?>
							Total de imóveis em destaque permitidos no portal: <?= $numero_destaques_permitidos ?></span>
						<?php } ?>

						<?php if ($com_super_destaques) { ?>
							Total de imóveis Super Destacados: <span id="super_destacados"><?= $super_destacados ?></span>
						<?php } else if ($campos_busca['site'] == 'mercadolivre') { ?>
							Total de imóveis Gold Premium: <span id="super_destacados"><?= $super_destacados ?></span>
						<?php } ?>

						<?php if ($com_destaque_especial) { ?>
							Total de imóveis Destaque Superior: <span id="destaque_especial"><?= $destaque_especial ?></span>
						<?php } ?>

						<?php if ($com_destaque_premium) { ?>
							Total de imóveis Destaque Exclusivo: <span id="destaque_premium"><?= $destaque_premium ?></span>
						<?php } ?>

						<?php if ($mostrar_qtd_imoveis_contratados) { ?>
							<div id="plano-contratado">
								<label>Plano Contratado</label>
								<input type="text" id="qtd_imoveis_contratados" class="mascara mascara-numero">
							</div>
						<?php } ?>
					</div>
				</div>
				<div class="aviso-portal"></div>
			</div>

			<h2>Filtragem de imóveis</h2>
			<div class="card">
				<?php if ($galvao) { ?>
					<div class="row-fluid">
						<div class="span3">
							<label>Transação</label>
							<?= form_dropdown('pg', $pgs, $campos_busca['pg'], 'id="pg" class="span12"'); ?>
						</div>
						<div class="span3">
							<label>Referencia</label>
							<?= form_input('referencia', $campos_busca['referencia'], 'class="span12"') ?>
						</div>
						<div class="span3">
							<label>Tipo de imóvel</label>
							<?= form_dropdown('tipo_id', $tipos, $campos_busca['tipo_id'], 'class="span12"'); ?>
						</div>
						<div class="span3">
							<label>Modo de trabalhar</label>
							<?= form_dropdown('modo', $modosdetrabalhar, $campos_busca['trabalhar_como_id'], 'class="span12"'); ?>
						</div>
					</div>
					<div class="row-fluid">
						<div class="span3">
							<label>Endereço</label>
							<?= form_input('endereco', $campos_busca['endereco'], 'class="span12"') ?>
						</div>
						<div class="span3">
							<label>Agência</label>
							<?= form_dropdown('agencia', $agencias, $campos_busca['agencia'], 'class="span12"'); ?>
						</div>
						<div class="span3">
							<label>Corretor</label>
							<?= form_dropdown('corretor_galvao', $corretores_galvao, $campos_busca['corretor_galvao'], 'class="span12"'); ?>
						</div>
						<div class="span3">
							<label for=""> </label>
							<button class="btn btn-success" type="submit">Filtrar</button>
						</div>
					</div>
				<?php } else { ?>
					<div class="row-fluid">
						<div class="span2">
							<label>Transação</label>
							<?= form_dropdown('pg', $pgs, $campos_busca['pg'], 'id="pg" class="span12"'); ?>
						</div>
						<div class="span2">
							<label>Referencia</label>
							<input type="text" name="referencia" class="span12">
						</div>
						<div class="span2">
							<label>Tipo de imóvel</label>
							<?= form_dropdown('tipo_id', $tipos, $campos_busca['tipo_id'], 'class="span12"'); ?>
						</div>
						<div class="span2">
							<label>Modo de trabalhar</label>
							<?= form_dropdown('modo', $modosdetrabalhar, $campos_busca['trabalhar_como_id'], 'class="span12"'); ?>
						</div>
						<div class="span2">
							<label>Endereço</label>
							<?= form_input('endereco', $campos_busca['endereco'], 'class="span12"') ?>
						</div>
						<div class="span2">
							<label>Corretor/Angariador</label>
							<?= form_dropdown('corretor_angariador_id', $angariadores, $campos_busca['corretor_angariador_id'], 'class="span12"'); ?>
						</div>
						<div class="span2">
							<label for=""> </label>
							<button class="btn btn-success" type="submit">Filtrar</button>
						</div>
					</div>
				<?php } ?>
			</div>
		</form>

		<!-- ================================================================ -->

		<table class="table">
			<tr style="border-top: 0;">
				<th>Referência</th>
				<th></th>
				<th>Modo de trabalhar</th>
				<th>Tipo</th>
				<th>Bairro</th>
				<th>Endereço</th>
				<th>Edifício</th>
				<th align="right">Valor</th>
				<th align="right">Área</th>
				<th>Fotos</th>
				<th style="min-width: 80px;">
					<?php if ($campos_busca['site'] == 'rodadadeimoveis' || $campos_busca['site'] == 'imoveisempinhais') { ?>
						Selecionar
					<?php } else { ?>
						<?php if (!$todos_imoveis) { ?>
							<input type="checkbox" <?= ($todos_selecionados) ? 'checked' : '' ?> id="selecionar-todos-filtro" class="selecionar-todos-filtro"> Selecionar Todos
						<?php } else { ?>
							<input type="checkbox" <?= ($todos_selecionados) ? 'checked' : '' ?> id="selecionar_todos"> Selecionar Todos
						<?php } ?>
					<?php } ?>
				</th>
				<th>
					<?php if ($campos_busca['site'] == 'imovelweb' || $campos_busca['site'] == 'casamineira') { ?>
						<select name="tipo_destaque_todos" id="tipo_destaque_todos">
							<option value="" selected>Opção de Destaque</option>
							<option value="nao">Nenhum</option>
							<option value="todos">Destaque</option>
							<option value="todos_especial">Super Destaque</option>
						</select>
					<?php } elseif ($campos_busca['site'] == 'zap') { ?>
						<select name="tipo_destaque_todos" id="tipo_destaque_todos">
							<option value="" selected>Opção de Destaque</option>
							<option value="nao">Simples</option>
							<option value="todos_especial">Destaque</option>
							<option value="todos_especial2">Super Destaque</option>
						</select>
					<?php } elseif ($campos_busca['site'] == 'mercadolivre') { ?>
						<select name="tipo_destaque_todos" id="tipo_destaque_todos">
							<option value="" selected>Opção de Destaque</option>
							<option value="nao">Silver</option>
							<option value="todos_especial">Gold</option>
							<option value="todos_especial2">Gold Premium</option>
						</select>
					<?php } elseif (in_array($campos_busca['site'], array('vivareal', 'buscacuritiba'))) { ?>
						<select name="tipo_destaque_todos" id="tipo_destaque_todos">
							<option value="" selected>Opção de Destaque</option>
							<option value="nao">Nenhum</option>
							<option value="todos">Destaque</option>
							<option value="todos_especial">Super Destaque</option>
							<option value="todos_especial3">Destaque Exclusivo</option>
							<option value="todos_especial2">Destaque Superior</option>
							<option value="todos_especial4">Destaque Triplo</option>
						</select>
					<?php } elseif (
						$campos_busca['site'] == 'imoviewtv' ||
						$campos_busca['site'] == 'rodadadeimoveis' ||
						$campos_busca['site'] == 'attria' ||
						$campos_busca['site'] == 'facebook' ||
						$campos_busca['site'] == 'creci'
					) { ?>
					<?php } else if ($campos_busca['site'] == 'imoveisempinhais') { ?>
						Destacar
					<?php } else if (($campos_busca['site'] != 'redeurbana')) { ?>
						<input type="checkbox" id="destacar_todos"> Destacar Todos
					<?php } ?>
				</th>
			</tr>

			<?php
			$total_destaques = 0;
			foreach ($imoveis as $imovel) {
				if (substr($imovel['foto_principal'], 0, 4) == 'http') {
					$imovel['foto_principal'] = $imovel['foto_principal'];
				} else {
					$imovel['foto_principal'] = ENDERECO_FOTOS . 'fotosimovel/' . $imovel['id'] . '/' . str_replace('.jpg', '_400.jpg', $imovel['foto_principal']);
				}

				$valor = '';
				if ($imovel['tipo_venda'] == 1) {
					$valor = ($imovel['valor_total']) ? number_format($imovel['valor_total'], 2, ',', '.') : '';
				} else if ($imovel['tipo_locacao'] == 1) {
					$valor = ($imovel['aluguel']) ? number_format($imovel['aluguel'], 2, ',', '.') : '';
				} else if ($imovel['tipo_temporada'] == 1) {
					$valor = ($imovel['valor_alta_temporada']) ? number_format($imovel['valor_alta_temporada'], 2, ',', '.') : '';
				} else if ($imovel['tipo_lancamento'] == 1) {
					$valor = ($imovel['valor_inicial']) ? number_format($imovel['valor_inicial'], 2, ',', '.') : '';
				}
			?>
				<tr id="linha_<?= $imovel['id'] ?>" data-valor="<?= $valor ?>" data-fotos="<?= $imovel['num_fotos'] ?>">
					<td><?= $imovel['referencia'] ?></td>
					<td><i class="icon-camera" data-photo="<?= $imovel['foto_principal'] ?>"></i></td>
					<td><?= $imovel['modo'] ?></td>
					<td><?= $imovel['tipo'] ?></td>
					<td><?= $imovel['bairro'] ?></td>
					<td>
						<span class="<?php if ($imovel['mostrar_endereco'] == 1) echo 'mostrar' ?>"><?= $imovel['endereco'] ?></span><span class="<?php if ($imovel['mostrar_numero'] == 1) echo 'mostrar' ?>">, <?= $imovel['numero'] ?></span>
						<span class="<?php if ($imovel['mostrar_complemento'] == 1) echo 'mostrar' ?>"><?= $imovel['complemento'] ?></span>
					</td>
					<td><?= $imovel['edificio'] ?></td>
					<td align="right"><?= $valor ?></td>
					<td align="right"><?= $imovel['area_total'] ?></td>
					<td><?= $imovel['num_fotos'] ?></td>
					<td>
						<?php if ($campos_busca['site'] == $imovel['selecionado'] || $todos_selecionados) { ?>
							<input class="parceiro" value="<?= $imovel['id'] ?>" type="checkbox" checked>
						<?php } else { ?>
							<input class="parceiro" value="<?= $imovel['id'] ?>" type="checkbox">
						<?php } ?>
					</td>
					<td>
						<?php if ($campos_busca['site'] == 'imovelweb' || $campos_busca['site'] == 'casamineira') {
							echo form_dropdown('tipos_destaque', $tipos_destaque_imovelweb, $imovel['destacar'], 'id="select-destaque-' . $imovel['id'] . '" data-id="' . $imovel['id'] . '"');
						} elseif ($campos_busca['site'] == 'zap') {
							echo form_dropdown('tipos_destaque', $tipos_destaque2, $imovel['destacar'], 'id="select-destaque-' . $imovel['id'] . '" data-id="' . $imovel['id'] . '"');
						} elseif ($campos_busca['site'] == 'mercadolivre') {
							echo form_dropdown('tipos_destaque', $tipos_destaque_mercadolivre, $imovel['destacar'], 'id="select-destaque-' . $imovel['id'] . '" data-id="' . $imovel['id'] . '"');
						} elseif (in_array($campos_busca['site'], array('vivareal', 'buscacuritiba'))) {
							echo form_dropdown('tipos_destaque', $tipos_destaque3, $imovel['destacar'], 'id="select-destaque-' . $imovel['id'] . '" data-id="' . $imovel['id'] . '"');
						} elseif (
							$campos_busca['site'] == 'imoviewtv' ||
							$campos_busca['site'] == 'rodadadeimoveis' ||
							$campos_busca['site'] == 'attria' ||
							$campos_busca['site'] == 'facebook' ||
							$campos_busca['site'] == 'creci'
						) {
						} else if ($campos_busca['site'] != 'redeurbana') {
							if ($imovel['destacar'] == 1) { ?>
								<input class="destacar" value="<?= $imovel['id'] ?>" type="checkbox" checked>
							<?php } else { ?>
								<input class="destacar" value="<?= $imovel['id'] ?>" type="checkbox">
						<?php }
						} ?>
					</td>
				</tr>
			<?php } ?>
		</table>

		<p style="margin-top: 50px; padding-bottom: 200px;">Última atualização <?= $data_atualizacao['data'] ?></p>

	</div>
</div>

<script type="text/javascript">
	var caminho_padrao = '<?= base_url(); ?>';
	var site_url = '<?= site_url(); ?>';
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js"></script>
<script src="<?= base_url(); ?>js/bootstrap.js"></script>
<script src="<?= base_url(); ?>js/admin/geral.js"></script>
<script src="<?= base_url(); ?>js/mascaras.js"></script>

<script type="text/javascript">
	var total = <?= ($total) ? $total : 0 ?>;
	var destacados = <?= ($destacados) ? $destacados : 0 ?>;
	var super_destacados = <?= (isset($super_destacados) && $super_destacados) ? $super_destacados : 0 ?>;
	var destaque_especial = <?= (isset($destaque_especial) && $destaque_especial) ? $destaque_especial : 0 ?>;
	var destaque_premium = <?= (isset($destaque_premium) && $destaque_premium) ? $destaque_premium : 0 ?>;
	var selecionados = <?= ($selecionados) ? $selecionados : 0 ?>;
	var pg = $('#pg').val();
	var chave = '<?= $imobiliaria['chave'] ?>';
	var portal_numero_permitidos = 0;
	var portal_numero_destaques_permitidos = 0;

	<?php if ($campos_busca['site'] == 'imoveisempinhais') { ?>
		portal_numero_permitidos = <?= $numero_imoveis_permitidos ?>;
		portal_numero_destaques_permitidos = <?= $numero_destaques_permitidos ?>;
	<?php } ?>

	//--------------------------------------------------------------------------

	var trocar_portal = false;
	var qtd_imoveis_contratados = new Array();
	var qtd_destaques_contratados = 99999999;

	<?php
	foreach ($qtd_imoveis_contratados as $item) {
		if (!isset($item['imoveis_permitidos']) || $item['imoveis_permitidos'] == '') $item['imoveis_permitidos'] = 0;
		echo "qtd_imoveis_contratados['{$item['portal']}'] = {$item['imoveis_permitidos']};";
		echo "var qtd_destaques_contratados = {$item['destaques']};";
	}
	?>

	$(document).ready(function() {

		if ($('#site_parceiro').val() == 'rodadadeimoveis' && selecionados > 0) bloquearSelecionar();

		$('#site_parceiro').change(function() {
			if ($(this).val() == '') {
				$('.aviso-portal').html('');
				$('#qtd_imoveis_contratados').val('');
			} else {
				opts = '';

				if (typeof qtd_imoveis_contratados[$(this).val()] !== 'undefined') {
					$('#qtd_imoveis_contratados').val(qtd_imoveis_contratados[$(this).val()]);
				}

				if ($(this).val() == 'imovelweb') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v3/' + chave;
				} else if ($(this).val() == 'minhaprimeiracasa') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v2/' + chave;
				} else if ($(this).val() == 'vivareal') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v1/' + chave;
				} else if ($(this).val() == 'zap') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v6/' + chave;
				} else if ($(this).val() == 'imovelmagazine') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v8/' + chave;
				} else if ($(this).val() == 'redeimoveis') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v10/' + chave;
				} else if ($(this).val() == 'chavesnamao') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v5/' + chave;
				} else if ($(this).val() == 'imoviewtv') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v11/' + chave;
				} else if ($(this).val() == 'canaldoimovel') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v12/' + chave;
				} else if ($(this).val() == 'mercadolivre') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v14/' + chave;
				} else if ($(this).val() == 'olx') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v15/' + chave;
				} else if ($(this).val() == 'chavefacilnacional') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v17/' + chave;
				} else if ($(this).val() == 'luxury') {
					link = 'https://painel.promentor.com.br/index.php/exportar/v18/' + chave;
				} else if ($(this).val() == 'casamineira') {
					link = 'https://painel.promentor.com.br/index.php/exportar/v19/' + chave;
				} else if ($(this).val() == 'imovomap') {
					link = 'https://painel.promentor.com.br/index.php/exportar/v20/' + chave;
				} else if ($(this).val() == 'attria') {
					link = 'http://painel.promentor.com.br/index.php/exportar/v21/' + chave;
				} else if ($(this).val() == 'facebook') {
					opts = '<option value="">Selecione</option><option value="tipo_venda" selected="selected">Venda</option><option value="tipo_locacao">Locação</option><option value="tipo_lancamento">Empreendimento</option>';
					link = 'https://painel.promentor.com.br/index.php/exportar/v24/' + chave;
				} else if ($(this).val() == 'creci') {
					link = 'https://painel.promentor.com.br/index.php/exportar/v25/' + chave;
				} else if ($(this).val() == 'redeurbana') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v26/' + chave;
				} else if ($(this).val() == 'redeurbanahub') {
					link = 'http://infocenterhost2.com.br/crm/index.php/exportar/v28/' + chave;
				} else if ($(this).val() == 'imoveisemcuritiba') {
					opts = '<option value="">Selecione</option><option value="tipo_venda" selected="selected">Venda</option><option value="tipo_locacao">Locação</option>';
					link = '';
				} else if ($(this).val() == 'loft') {
					link = 'https://painel.promentor.com.br/index.php/exportar/v26/' + chave;
				} else if ($(this).val() == 'redeuna') {
					link = 'https://painel.promentor.com.br/index.php/exportar/v27/' + chave;
				} else if ($(this).val() == 'buscacuritiba') {
					link = 'https://painel.promentor.com.br/index.php/exportar/v29/' + chave;
				} else {
					link = '';
				}

				//popularTransacao(opts);

				if (link != '') {
					$('.aviso-portal').html('Informe ao portal <b>' + $(this).find(':selected').text() + ',</b> que a partir de agora, a imobiliária <?= $imobiliaria['nome'] ?> fará a integração InfoCenter-Promentor, através do link: <p class="link-exportacao">' + link + '</p>');
					if ($(this).val() == 'olx') {
						$('.aviso-portal').addClass('olx');
					}
				}

				if (trocar_portal) $('#buscar-relatorio').find('button[type="submit"').trigger('click');

				trocar_portal = true;
			}
		});

		function popularTransacao(opts) {
			if (opts == '') { //Colocar todas
				$('#pg').html('<option value="">Selecione</option><option value="tipo_venda" selected="selected">Venda</option><option value="tipo_locacao">Locação</option><option value="tipo_lancamento">Lançamento</option><option value="tipo_temporada">Temporada</option>')
			} else {
				$('#pg').html(opts);
			}
		}

		$('#site_parceiro').trigger('change');

		$('#selecionar_todos').click(function() {
			site_parceiro = $('#site_parceiro').val();

			$('.loading').show();

			if (site_parceiro != '') {
				var parceiros = new Array();
				if ($(this).is(':checked')) {
					$('.parceiro').each(function(i) {
						parceiros[i] = $(this).val();
					});
					refs = parceiros.join();
					opcao = 'adicionar';
				} else {
					$('.parceiro').attr('checked', false);
					$('.parceiro').each(function(i) {
						parceiros[i] = $(this).val();
					});
					refs = parceiros.join();
					opcao = 'remover';

					selecionados = 0;
					$('#selecionados').html(selecionados);
				}

				$.ajax({
					type: 'POST',
					async: false,
					url: 'selecionar_todos',
					data: {
						o: opcao,
						refs: refs,
						pg: pg,
						parceiro: site_parceiro,
						todos: 1
					},
					success: function(xml) {
						$('.loading').hide();

						if (opcao == 'adicionar') {
							$('.parceiro').attr('checked', true);
							selecionados = total;
							$('#selecionados').html(selecionados);
						}
					},
					error: function(xhr) {
						erro = xhr.responseText;
						erro = JSON.parse(erro);
						if (erro && erro.mensagem) alert(erro.mensagem);
						$('.loading').hide();

						$('#selecionar_todos').attr('checked', false);
					}
				});
			} else {
				alert('Selecione o site parceiro');
				$('#selecionar_todos').attr('checked', false);
			}

			$('.loading').hide();
		});

		//--------------------------------------------------------------------------

		$('.selecionar-todos-filtro').click(function() {
			var ids = '';
			var separador = '';
			site_parceiro = $('#site_parceiro').val();

			$('input.parceiro').each(function() {
				ids += separador + $(this).val();
				separador = ',';
			});

			if (!$(this).is(':checked')) {
				// Remover todos os imoveis da filtragem
				$.ajax({
					type: 'POST',
					async: true,
					url: 'remover_batch',
					dataType: 'html',
					data: {
						id: ids,
						pg: pg,
						site: site_parceiro,
					},
					success: function(xml) {
						$('input.parceiro').attr('checked', false);
						$.ajax({
							type: 'GET',
							async: true,
							dataType: 'html',
							url: 'selecionados',
							data: {
								pg: pg,
								site: site_parceiro,
							},
							success: function(selecionados) {
								$('#selecionados').html(selecionados);
							}
						});
					},
					beforeSend: function() {
						$('.loading').addClass('active');
					},
					complete: function() {
						$('.loading').removeClass('active');
					}
				});

			} else {
				// Adicionar os imóveis da filtragem

				$.ajax({
					type: 'POST',
					async: true,
					url: 'adicionar_batch',
					dataType: 'html',
					data: {
						id: ids,
						pg: pg,
						site: site_parceiro,
					},
					success: function(xml) {
						$('input.parceiro').attr('checked', true);
						$.ajax({
							type: 'GET',
							async: true,
							dataType: 'html',
							url: 'selecionados',
							data: {
								pg: pg,
								site: site_parceiro,
							},
							success: function(selecionados) {
								$('#selecionados').html(selecionados);
							}
						});
					},
					beforeSend: function() {
						$('.loading').addClass('active');
					},
					complete: function() {
						$('.loading').removeClass('active');
					},
					error: function(xhr) {
						erro = xhr.responseText;
						erro = JSON.parse(erro);
						if (erro && erro.mensagem) alert(erro.mensagem);
						$('.loading').hide();

						$('#selecionar_todos').attr('checked', false);
					}
				});
			}
		});

		//--------------------------------------------------------------------------

		$('#destacar_todos').click(function() {
			site_parceiro = $('#site_parceiro').val();

			$('.loading').show();

			if ($('#selecionar_todos').is(':checked')) {
				if (site_parceiro != '') {
					var parceiros = new Array();
					if ($(this).is(':checked')) {

						var quantidade_permitida = qtd_destaques_contratados;
						if (quantidade_permitida != '') {
							quantidade_permitida = parseInt(quantidade_permitida);
						} else {
							quantidade_permitida = 99999999;
						}
						num_imoveis = $('.destacar').length;
						if (num_imoveis > quantidade_permitida) {
							alert('Não é possível destacar todos pois excede o número de destaques contratados');
							$('.loading').hide();
							return false;
						}

						$('.destacar').attr('checked', true);
						$('.destacar').each(function(i) {
							parceiros[i] = $(this).val();
						});
						refs = parceiros.join();
						opcao = 'destacar';

						destacados = total;
						$('#destacados').html(destacados);

					} else {
						$('.destacar').attr('checked', false);
						$('.destacar').each(function(i) {
							parceiros[i] = $(this).val();
						});
						refs = parceiros.join();
						opcao = 'remover_destaque';

						destacados = '0';
						$('#destacados').html(destacados);
					}

					$.ajax({
						type: 'GET',
						async: false,
						url: 'destacar_todos',
						data: {
							o: opcao,
							refs: refs,
							pg: pg,
							parceiro: site_parceiro
						},
						success: function(xml) {
							$('.loading').hide();
						}
					});
				} else {
					alert('Selecione o site parceiro');
					$('#destacar_todos').attr('checked', false);
				}

			} else {
				alert('Todos os imóveis devem estar selecionados antes de destacar');
				$('#destacar_todos').attr('checked', false);
			}

			$('.loading').hide();
		});

		//--------------------------------------------------------------------------

		$('#tipo_destaque_todos').change(function() {

			var filtro = false;
			if ($("#selecionar-todos-filtro").length) filtro = true;

			site_parceiro = $('#site_parceiro').val();
			opcao = $(this).val();

			$('.loading').show();

			if (filtro) {

				var nao_destacados = $('input.parceiro:checkbox:not(:checked)').length;

				if (nao_destacados > 0) {
					alert('Todos os imóveis devem estar selecionados antes de destacar');
					$('#tipo_destaque_todos').val('');
				} else {

					tmp = $('input.parceiro:checkbox:(:checked)').map(function() {
						return this.value;
					}).get();
					var ids = tmp.join(',');

					$.ajax({
						type: 'POST',
						async: false,
						url: 'destacar_batch',
						data: {
							o: opcao,
							ids: ids,
							pg: pg,
							parceiro: site_parceiro
						},
						success: function(xml) {
							$('.loading').hide();
							//TODO: Retornar o total de destacados ao alterar destaque com filtragem

							$("#buscar-relatorio").submit();
						}
					});
				}

			} else {
				if ($('#selecionar_todos').is(':checked')) {
					if (site_parceiro != '') {
						var parceiros = new Array();
						if ($(this).is(':checked')) {
							$('.destacar').attr('checked', true);
							$('.destacar').each(function(i) {
								parceiros[i] = $(this).val();
							});
							refs = parceiros.join();

							destacados = total;
							$('#destacados').html(destacados);

						} else {
							$('.destacar').attr('checked', false);
							$('.destacar').each(function(i) {
								parceiros[i] = $(this).val();
							});
							refs = parceiros.join();

							destacados = '0';
							$('#destacados').html(destacados);
						}

						$.ajax({
							type: 'GET',
							async: false,
							url: 'destacar_todos',
							data: {
								o: opcao,
								refs: refs,
								pg: pg,
								parceiro: site_parceiro
							},
							success: function(xml) {
								$('.loading').hide();
							}
						});
					} else {
						alert('Selecione o site parceiro');
						$('#tipo_destaque_todos').val('');
					}

					if (opcao == 'nao') {
						$('table td select').val(0);
					} else if (opcao == 'todos') {
						$('table td select').val(1);
					} else if (opcao == 'todos_especial') {
						$('table td select').val(2);
					}

				} else {
					alert('Todos os imóveis devem estar selecionados antes de destacar');
					$('#tipo_destaque_todos').val('');
				}
			}

			$('.loading').hide();
		});

		//--------------------------------------------------------------------------

		$('table td select').on('focusin', function() {
			$(this).data('val', $(this).val());
		});

		//--------------------------------------------------------------------------

		$('table td select').change(function() {
			site_parceiro = $('#site_parceiro').val();
			opcao = $(this).val();

			$('.loading').show();

			if ($('.parceiro[value=' + $(this).attr('data-id') + ']').is(':checked')) {
				if (site_parceiro != '') {
					var refs = $(this).attr('data-id');
					if (opcao == '0') {

						var prev = $(this).data('val');
						var current = $(this).val();

						if (prev == 1) {
							destacados = destacados - 1;
							$('#destacados').html(destacados);
						} else if (prev == 2) {
							super_destacados = super_destacados - 1;
							$('#super_destacados').html(super_destacados);
						} else if (prev == 3) {
							destaque_especial = destaque_especial - 1;
							$('#destaque_especial').html(destaque_especial);
						} else if (prev == 4) {
							destaque_premium = destaque_premium - 1;
							$('#destaque_premium').html(destaque_premium);
						}

					} else {

						if (site_parceiro == 'zap') {

						} else if (site_parceiro == 'mercadolivre') {

							var prev = $(this).data('val');
							var current = $(this).val();
							if (prev == 2) {
								destacados = destacados - 1;
								$('#destacados').html(destacados);
							} else if (prev == 3) {
								super_destacados = super_destacados - 1;
								$('#super_destacados').html(super_destacados);
							}

							if (opcao == 2) {
								destacados = destacados + 1;
								$('#destacados').html(destacados);
							} else if (opcao == 3) {
								super_destacados = super_destacados + 1;
								$('#super_destacados').html(super_destacados);
							}

						} else {

							var prev = $(this).data('val');
							var current = $(this).val();
							if (prev == 1) {
								destacados = destacados - 1;
								$('#destacados').html(destacados);
							} else if (prev == 2) {
								super_destacados = super_destacados - 1;
								$('#super_destacados').html(super_destacados);
							}

							if (opcao == 1) {
								destacados = destacados + 1;
								$('#destacados').html(destacados);
							} else if (opcao == 2) {
								super_destacados = super_destacados + 1;
								$('#super_destacados').html(super_destacados);
							} else if (opcao == 3) {
								destaque_especial = destaque_especial + 1;
								$('#destaque_especial').html(destaque_especial);
							} else if (opcao == 4) {
								destaque_premium = destaque_premium + 1;
								$('#destaque_premium').html(destaque_premium);
							}
						}
					}

					$.ajax({
						type: 'GET',
						async: false,
						url: 'destacar2',
						data: {
							opcao: opcao,
							refs: refs,
							pg: pg,
							parceiro: site_parceiro
						},
						success: function(xml) {
							$('.loading').hide();
						}
					});
				} else {
					alert('Selecione o site parceiro');
					$(this).attr('checked', false);
				}
				$(this).blur();
			} else {
				alert('Este imóvel deve estar selecionado');
				$(this).attr('checked', false);
			}

			$('.loading').hide();
		});

		//--------------------------------------------------------------------------

		$('.parceiro').click(function() {
			site_parceiro = $('#site_parceiro').val();
			$('.loading').show();
			var permitir_selecao = false;

			if (site_parceiro != '') {
				var refs = $(this).val();
				if ($(this).is(':checked')) {

					var permitir2 = true;
					if (site_parceiro == 'imoveisemcuritiba') {

						console.log($('#linha_' + refs).attr('data-valor'));

						if ($('#linha_' + refs).attr('data-valor') == '') {
							if ($('#pg').val() == 'tipo_venda') {
								alert('Imóvel não permitido, necessário possuir valor de Venda');
							} else if ($('#pg').val() == 'tipo_locacao') {
								alert('Imóvel não permitido, necessário possuir valor de Locação');
							}
							permitir2 = false;
						}

						if ($('#linha_' + refs).attr('data-fotos') == '') {
							alert('Imóvel não permitido, necessário possuir alguma foto');
							permitir2 = false;
						}
					}

					if (permitir2 && verificarPermissao('selecionar')) {
						permitir_selecao = true;
						opcao = 'adicionar';
						if (site_parceiro == 'rodadadeimoveis') bloquearSelecionar();
					}
				} else {
					permitir_selecao = true;
					opcao = 'remover';
					selecionados = selecionados - 1;
					$("input[value='" + $(this).val() + "']").attr('checked', false);
					if (site_parceiro == 'rodadadeimoveis') liberarSelecionar();

					console.log('remover');
				}

				var quantidade_permitida = $('#qtd_imoveis_contratados').val();

				if (quantidade_permitida != '') {
					quantidade_permitida = parseInt(quantidade_permitida);
				} else {
					quantidade_permitida = 99999999;
				}

				if (selecionados >= quantidade_permitida) {
					permitir_selecao = false;
				}

				if (permitir_selecao) {
					$.ajax({
						type: 'GET',
						async: false,
						url: opcao,
						data: {
							refs: refs,
							pg: pg,
							parceiro: site_parceiro
						},
						success: function(xml) {
							$('.loading').hide();

							if (opcao == 'adicionar') {
								selecionados = selecionados + 1;
							}

							$('#selecionados').html(selecionados);
						}
					});
				} else {
					if (permitir2) {
						alert('Limite de imóveis atingido!\r\n\r\nPara selecionar imóveis, entrar em contato com o Portal para ampliar seu plano de divulgação');
					}
					$(this).attr('checked', false);
				}
			} else {
				alert('Selecione o site parceiro');
				$(this).attr('checked', false);
			}

			$('.loading').hide();
		});

		//--------------------------------------------------------------------------

		$('.destacar').click(function() {
			site_parceiro = $('#site_parceiro').val();
			$('.loading').show();
			var permitir_selecao = false;

			if ($('.parceiro[value=' + $(this).val() + ']').is(':checked')) {

				if (site_parceiro != '') {
					var refs = $(this).val();

					var quantidade_permitida = qtd_destaques_contratados;

					if (quantidade_permitida != '') {
						quantidade_permitida = parseInt(quantidade_permitida);
					} else {
						quantidade_permitida = 99999999;
					}

					if (destacados > quantidade_permitida) {
						permitir_selecao = false;
					}

					if ($(this).is(':checked')) {
						if (verificarPermissao('destacar')) {
							if (destacados < quantidade_permitida) {
								permitir_selecao = true;
								opcao = 'destacar';
								destacados = destacados + 1;
								$('#destacados').html(destacados);
							}
						}
					} else {
						permitir_selecao = true;
						opcao = 'remover_destaque';
						destacados = destacados - 1;
						$('#destacados').html(destacados);
					}

					if (permitir_selecao) {
						$.ajax({
							type: 'GET',
							async: false,
							url: opcao,
							data: {
								refs: refs,
								pg: pg,
								parceiro: site_parceiro
							},
							success: function(xml) {
								$('.loading').hide();
							}
						});
					} else {
						alert('Foi atingido o número limite de imóveis destacados');
						$(this).attr('checked', false);
					}
				} else {
					alert('Selecione o site parceiro');
					$(this).attr('checked', false);
				}
			} else {
				alert('Este imóvel deve estar selecionado');
				$(this).attr('checked', false);
			}

			$('.loading').hide();
		});

		$('.icon-camera').hover(
			function() {
				foto = $(this).attr('data-photo');
				$(this).parent().append($('<img class="foto-imovel-hover" src="' + foto + '">'));
			},
			function() {
				$(this).parent().find('img').remove();
			}
		);

		$('#qtd_imoveis_contratados').blur(function() {
			console.log('Salvar a quantidade de contratados para o portal ' + $('#site_parceiro').val());

			$.ajax({
				type: 'POST',
				async: false,
				url: 'salvar_qtd_contratados',
				data: {
					portal: $('#site_parceiro').val(),
					qtd: $(this).val()
				}
			});
		});

		//-----------------------------------------------------------------------

		$('.btn-excel').click(function() {

			if ($('#site_parceiro').val() == '') {
				alert('Selecione o site parceiro');
				return false;
			}

			var el = $('#buscar-relatorio');

			el.attr({
				'action': el.attr('action').replace('exportacao/selecionar', 'exportacao/selecionar?format=excel'),
				'target': '_blank'
			});

			el.submit();

			el.attr({
				'action': el.attr('action').replace('?format=excel', ''),
				'target': ''
			});
		});
	});

	function bloquearSelecionar() {
		console.log('bloquear');
		$('.parceiro').each(function() {
			if (!$(this).is(':checked')) {
				$(this).hide();
			}
		})
	}

	function liberarSelecionar() {
		console.log('liberar');
		$('.parceiro').show();
	}

	function verificarPermissao(acao) {
		if (acao == 'selecionar' && portal_numero_permitidos > 0) {
			if (portal_numero_permitidos <= selecionados) return false
		}

		if (acao == 'destacar' && portal_numero_destaques_permitidos > 0) {
			if (portal_numero_destaques_permitidos <= destacados) return false
		}
		return true
	}
</script>


</body>

</html>