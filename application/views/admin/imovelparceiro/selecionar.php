<?php
$ci->load->view('admin/template/header');
$ci->load->view('admin/template/menu');

$estado_padrao = $ci->session->userdata['imobiliaria_estado'];
$cidade_padrao = $ci->session->userdata['imobiliaria_cidade_id'];

if (isset($_GET['parceiro']) && $_GET['parceiro'] == 1268) {
	$estado_padrao = '';
	$cidade_padrao = '';
}
?>

<div class="loading"></div>

<div class="container">
	<div class="conteudo_padrao conteudo show-grid">
		<h1 class="titulo">Selecionar imóveis de parceiros</h1>
		<div class="botoes_cadastro" style="margin-top: -45px;">
			<div class="btn-group botoes-exportar">
				<a class="btn btn-inverse btn-excel" data-original-title="Exportar para excel" data-placement="top" rel="tooltip"><i class="icon-csv icon-white"></i></a>
			</div>
		</div>

		<form id="buscar-relatorio" method="post" action="<?= site_url() ?>/admin/imovelparceiro/selecionar" onsubmit="return validarBusca()">
			<h2>Parceiro</h2>
			<div class="box-principal card">
				<div class="row um-item">
					<div class="input-checkbox">
						<label>Site Parceiro</label>
						<?php
						if (isset($_GET['parceiro']) && $_GET['parceiro'] != '') $_POST['parceiro_id'] = $_GET['parceiro'];
						echo form_dropdown('parceiro_id', $parceiros, (isset($_POST['parceiro_id'])) ? $_POST['parceiro_id'] : '', 'id="parceiro_id" required"');
						?>
					</div>

					<?php if (isset($_POST['parceiro_id'])) { ?>
						<div class="right">
							Total de imoveis na Carteira: <?= $total ?><br>
							Total de imoveis Selecionados: <span id="selecionados"><?= $selecionados ?></span><br>
						</div>
					<?php } ?>
				</div>
			</div>

			<h2>Filtragem de imóveis</h2>
			<div class="card">
				<div class="row tres-por-linha">
					<div class="input">
						<label>Transação</label>
						<?php
						if (isset($_GET['parceiro']) && $_GET['parceiro'] == 1268) {
							echo '<select name="pg" id="pg" required=""><option value="tipo_venda" selected>Venda</option></select>';
						} else {
							echo form_dropdown('pg', $pgs, (isset($_POST['pg'])) ? $_POST['pg'] : '', 'id="pg" required');
						}
						?>
					</div>
					<div class="input">
						<label>Referência</label>
						<?= form_input('referencia', (isset($_POST['referencia'])) ? $_POST['referencia'] : '') ?>
					</div>
					<div class="input">
						<label>Tipo de imóvel</label>
						<?= form_dropdown('tipo_id', $tipos, (isset($_POST['tipo_id'])) ? $_POST['tipo_id'] : '', 'id="tipo_id"'); ?>
					</div>
				</div>
				<div class="row tres-por-linha">
					<div class="input">
						<label>Endereço</label>
						<?= form_input('endereco', (isset($_POST['endereco'])) ? $_POST['endereco'] : '') ?>
					</div>
					<div class="input">
						<label>Situação de parceria <div class="tooltip tooltip-seo" data-text="Sem parceria: Imóveis que você ainda não selecionou, nem duplicou para sua base. Parceria efetuada: Imóveis que você já possui parceria. Duplicado: Imóveis que você duplicou para a sua base"></div></label>
						<?= form_dropdown('situacao', array(
							'' => 'Selecione',
							'0' => 'Sem parceria',
							'1' => 'Parceria efetuada',
							'2' => 'Duplicado'
						), (isset($_POST['situacao'])) ? $_POST['situacao'] : ''); ?>
					</div>
					<div class="input input-append date" data-date-format="dd/mm/yyyy">
						<label>Atualizado após</label>
						<?= form_input('data_atualizacao', (isset($_POST['data_atualizacao'])) ? $_POST['data_atualizacao'] : '', 'class="mascara mascara-data"') ?>
						<span class="add-on"><i class="icon-calendar icon-white"></i></span>
					</div>
				</div>
				<div class="row tres-por-linha">
					<div class="input">
						<label>UF</label>
						<?= form_dropdown('estado', $estados, (isset($_POST['estado'])) ? $_POST['estado'] : '', 'onchange="estadoCidade($(this).val(),\'iCidade\')" id="iEstado"') ?>
					</div>
					<div class="input">
						<label>Cidade</label>
						<?= form_dropdown('cidade_id', $cidades, (isset($_POST['cidade_id'])) ? $_POST['cidade_id'] : '', 'onchange="cidadeBairro($(this).val(),\'iBairro\')" id="iCidade"') ?>
					</div>
					<div class="input">
						<label>Bairro</label>
						<?= form_dropdown('bairro_id', $bairros, (isset($_POST['bairro_id'])) ? $_POST['bairro_id'] : '', 'id="iBairro"') ?>
					</div>
				</div>
				<div class="row tres-por-linha">
					<div class="input">
						<label>Status</label>
						<?= form_dropdown('estagio_da_obra', $estagios_da_obra, (isset($_POST['estagio_da_obra'])) ? $_POST['estagio_da_obra'] : '') ?>
					</div>
					<div class="input">
						<label>Finalidade</label>
						<?= form_dropdown('tipo_de_uso', $tipos_de_uso, (isset($_POST['tipo_de_uso'])) ? $_POST['tipo_de_uso'] : '') ?>
					</div>
					<div class="input">
						<label>Tipo</label>
						<?= form_dropdown('tipo_construcao', $tipos_construcao, (isset($_POST['tipo_construcao'])) ? $_POST['tipo_construcao'] : '') ?>
					</div>
				</div>
				<div class="row tres-por-linha">
					<div class="input">
						<label>Incorporador</label>
						<?= form_input('construtora', (isset($_POST['construtora'])) ? $_POST['construtora'] : '') ?>
					</div>
					<div class="input input-comissao">
						<?php
						$operadores = array('>=' => 'Maior ou igual a', '=' => 'Igual a', '<=' => 'Menor ou igual a');
						?>
						<label>Comissão</label>
						<?= form_dropdown('comissao_operador', $operadores, (isset($_POST['comissao_operador'])) ? $_POST['comissao_operador'] : '') ?>
						<?= form_input('comissao', (isset($_POST['comissao'])) ? $_POST['comissao'] : '', 'class="mascara mascara-float"') ?>
						%
					</div>
					<div class="input input-de-ate">
						<label>Valor de Venda</label>
						<div>
							<?= form_input('valor_de', (isset($_POST['valor_de'])) ? $_POST['valor_de'] : '', 'class="mascara mascara-moeda"') ?>
							<span>a</span>
							<?= form_input('valor_ate', (isset($_POST['valor_ate'])) ? $_POST['valor_ate'] : '', 'class="mascara mascara-moeda"') ?>
						</div>
					</div>
				</div>
				<div class="row tres-por-linha">
					<div class="input input-de-ate">
						<label>Metragem Útil</label>
						<div>
							<?= form_input('area_privativa_de', (isset($_POST['area_privativa_de'])) ? $_POST['area_privativa_de'] : '', 'class="mascara mascara-numero"') ?>
							<span>a</span>
							<?= form_input('area_privativa_ate', (isset($_POST['area_privativa_ate'])) ? $_POST['area_privativa_ate'] : '', 'class="mascara mascara-numero"') ?>
						</div>
					</div>
					<div class="input input-de-ate">
						<label>Metragem Total</label>
						<div>
							<?= form_input('area_total_de', (isset($_POST['area_total_de'])) ? $_POST['area_total_de'] : '', 'class="mascara mascara-numero"') ?>
							<span>a</span>
							<?= form_input('area_total_ate', (isset($_POST['area_total_ate'])) ? $_POST['area_total_ate'] : '', 'class="mascara mascara-numero"') ?>
						</div>
					</div>
					<div class="input input-de-ate">
						<label>Metragem Terreno</label>
						<div>
							<?= form_input('area_terreno_de', (isset($_POST['area_terreno_de'])) ? $_POST['area_terreno_de'] : '', 'class="mascara mascara-numero"') ?>
							<span>a</span>
							<?= form_input('area_terreno_ate', (isset($_POST['area_terreno_ate'])) ? $_POST['area_terreno_ate'] : '', 'class="mascara mascara-numero"') ?>
						</div>
					</div>
				</div>
				<div class="row tres-por-linha">
					<div class="input">
						<label>Edifício</label>
						<input type="text" name="edificio" value="<?= (isset($_POST['edificio'])) ? $_POST['edificio'] : '' ?>">
					</div>
				</div>

				<?php
				// Somente para imóveis da caixa
				if (isset($_POST['parceiro_id']) && $_POST['parceiro_id'] == 1268) {
					$modalidades = array('' => 'Selecione', 'Venda direta' => 'Venda direta', '2º Leilão SFI' => '2º Leilão SFI');
					$situacoes = array('' => 'Selecione', 'Ocupado' => 'Ocupado', 'Desocupado' => 'Desocupado');
					$operadores = array('>=' => 'Maior ou igual a', '=' => 'Igual a', '<=' => 'Menor ou igual a');
					$sim_nao = array('' => 'Selecione', '1' => 'Sim', '0' => 'Não');
				?>
					<div class="row tres-por-linha">
						<div class="input">
							<label>Modalidade</label>
							<?= form_dropdown('modalidade', $modalidades, (isset($_POST['modalidade'])) ? $_POST['modalidade'] : '') ?>
						</div>
						<div class="input">
							<label>Situação</label>
							<?= form_dropdown('situacao2', $situacoes, (isset($_POST['situacao2'])) ? $_POST['situacao2'] : '') ?>
						</div>
						<div class="input input-comissao">
							<label>Deságio</label>
							<?= form_dropdown('desconto_operador', $operadores, (isset($_POST['desconto_operador'])) ? $_POST['desconto_operador'] : '') ?>
							<?= form_input('desconto_calculado', (isset($_POST['desconto_calculado'])) ? $_POST['desconto_calculado'] : '', 'class="mascara mascara-float"') ?>
							%
						</div>
					</div>
					<div class="row tres-por-linha">
						<div class="input">
							<label>Aceita Financiamento</label>
							<?= form_dropdown('financiamento', $sim_nao, (isset($_POST['financiamento'])) ? $_POST['financiamento'] : '') ?>
						</div>
						<div class="input">
							<label>Aceita FGTS</label>
							<?= form_dropdown('fgts', $sim_nao, (isset($_POST['fgts'])) ? $_POST['fgts'] : '') ?>
						</div>
						<div class="input">
							<label>Aceita Parcelamento</label>
							<?= form_dropdown('parcelamento', $sim_nao, (isset($_POST['parcelamento'])) ? $_POST['parcelamento'] : '') ?>
						</div>
					</div>
					<div class="row tres-por-linha">
						<div class="input">
							<label>Aceita Consórcio</label>
							<?= form_dropdown('consorcio', $sim_nao, (isset($_POST['consorcio'])) ? $_POST['consorcio'] : '') ?>
						</div>
						<div class="input">
							<label>Descrição (contém)</label>
							<?= form_input('descricao', (isset($_POST['descricao'])) ? $_POST['descricao'] : '') ?>
						</div>
						<div class="input"></div>
					</div>
				<?php } ?>

				<div class="row botoes">
					<button class="btn btn-success" type="submit">Filtrar</button>
					<br class="clear">
				</div>
			</div>
		</form>

		<!-- ================================================================ -->

		<?php if (isset($imoveis) && sizeof($imoveis) == 0) {
			echo '<p>Nenhum imóvel encontrado</p>';
		} else if (isset($imoveis)) { ?>

			<p>
				<!--label class="selecionar-todos"><input type="checkbox" id="selecionar_todos" <?= ($selecionar_todos) ? 'checked' : '' ?>>Selecionar Todos</label-->
				<button class="remover-todos-filtro btn-danger" type="button">Remover todos da busca</button>
				<button class="selecionar-todos-filtro btn-success" type="button">Selecionar todos da busca</button>
			</p>

			<table class="table">
				<tr style="border-top: 0;">
					<th>Referência</th>
					<th>Cidade</th>
					<th>Bairro</th>
					<th>Endereço</th>
					<th align="right;">Valor</th>
					<th align="right">Área</th>
					<th>Edifício</th>
					<th>OBS</th>
					<th style="width: 100px;">Selecionar para parceria</th>
					<th style="width: 100px;">Não incluir na parceria</th>
					<?php if (
						$_POST['parceiro_id'] == '355' ||
						($_POST['parceiro_id'] == '1305' || $this->session->userdata['imobiliaria_id'] == '1305') ||
						($_POST['parceiro_id'] == '1192' && $this->session->userdata['imobiliaria_id'] == '1495')
					) echo '<th>Duplicar</th>' ?>
					<th></th>
				</tr>

				<?php
				$total_destaques = 0;
				foreach ($imoveis as $imovel) {
				?>
					<tr>
						<td><?= $imovel['referencia'] ?></td>
						<td><?= $imovel['tipo'] ?> em <?= $imovel['cidade'] ?></td>
						<td><?= $imovel['bairro'] ?></td>
						<td><?= $imovel['endereco'] ?></td>
						<td align="right">
							<?php
							if ($imovel['tipo_venda'] == 1) {
								echo ($imovel['valor_total']) ? number_format($imovel['valor_total'], 2, ',', '.') : ' ';
							} else if ($imovel['tipo_locacao'] == 1) {
								echo ($imovel['aluguel']) ? number_format($imovel['aluguel'], 2, ',', '.') : ' ';
							} else if ($imovel['tipo_temporada'] == 1) {
								echo ($imovel['valor_alta_temporada']) ? number_format($imovel['valor_alta_temporada'], 2, ',', '.') : ' ';
							} else if ($imovel['tipo_lancamento'] == 1) {
								echo ($imovel['valor_inicial']) ? number_format($imovel['valor_inicial'], 2, ',', '.') : ' ';
							}
							?>
						</td>
						<td align="right"><?= $imovel['area_total'] ?></td>
						<td><?= $imovel['edificio'] ?></td>
						<td style="overflow: hidden; min-width: 200px; max-width: 0;"><?= nl2br($imovel['observacoes']) ?></td>
						<td class="parceiro_sim esconder<?= $imovel['id'] ?>" id="parceiro_sim<?= $imovel['id'] ?>">
							<?php if ($imovel['orulo_id'] != $imovel['id']) { ?>
								<input type="radio" name="imovel<?= $imovel['id'] ?>" id="imovel<?= $imovel['id'] ?>_sim" value="<?= $imovel['id'] ?>" data-referencia="<?= $imovel['referencia'] ?>" <?php if ($imovel['parceira']) echo 'checked' ?>>
								<label for="imovel<?= $imovel['id'] ?>_sim"></label>
							<?php } ?>
						</td>
						<td class="parceiro_nao esconder<?= $imovel['id'] ?>">
							<?php if ($imovel['orulo_id'] != $imovel['id']) { ?>
								<input type="radio" name="imovel<?= $imovel['id'] ?>" id="imovel<?= $imovel['id'] ?>_nao" value="<?= $imovel['id'] ?>" data-referencia="<?= $imovel['referencia'] ?>" <?php if (!$imovel['parceira']) echo 'checked' ?>>
								<label for="imovel<?= $imovel['id'] ?>_nao"></label>
							<?php } ?>
						</td>
						<?php if ($imovel['orulo_id'] == $imovel['id']) { ?>
							<td style="vertical-align: middle;" id="deletar<?= $imovel['id'] ?>">
								<a href="javascript:void(0)" onclick="removerImovelOrulo(<?= $imovel['id'] ?>)" class="remover" data-id="<?= $imovel['id'] ?>">
									<i class="icon-remove icon-white"></i>
								</a>
							</td>
						<?php } else if ($_POST['parceiro_id'] == '355' || ($_POST['parceiro_id'] == '1305' || $this->session->userdata['imobiliaria_id'] == '1305') || ($_POST['parceiro_id'] == '1192' && $this->session->userdata['imobiliaria_id'] == '1495')) { ?>
							<td style="vertical-align: middle;" id="deletar<?= $imovel['id'] ?>">
								<?= anchor('admin/imovel/duplicar?id=' . $imovel['id'], '<i class="icon-plus-sign icon-white"></i>', 'class="duplicar" target="_blank" data-id="' . $imovel['id'] . '"') ?>
							</td>
						<?php } ?>
						<td style="vertical-align: middle;">
							<a href="javascript:void(0)" onclick="verFicha('<?= str_replace('tipo_', '', $_POST['pg']) ?>','<?= $imovel['id'] ?>')" class="ver-no-site"><i class="icon-eye-open icon-white"></i></a>
						</td>
					</tr>
				<?php } ?>
			</table>

		<?php } ?>
	</div>
</div>

<script type="text/javascript">
	var caminho_padrao = '<?= base_url(); ?>';
	var site_url = '<?= site_url(); ?>';
	var site_imobiliaria = '<?= $ci->session->userdata['site'] ?>';
</script>
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.2/jquery.min.js"></script>
<script type="text/javascript" src="<?= base_url(); ?>js/bootstrap.js"></script>
<script type="text/javascript" src="<?= base_url(); ?>js/admin/geral.js"></script>
<script type="text/javascript" src="<?= base_url(); ?>js/mascaras.js"></script>
<script type="text/javascript" src="<?= base_url() ?>/js/bootstrap-datepicker.js"></script>

<script type="text/javascript">
	var total = <?= $total ?>;
	var selecionados = <?= $selecionados ?>;
	var pg = $('#pg').val();

	//--------------------------------------------------------------------------

	$(document).ready(function() {
		imobiliaria = $('#parceiro_id').val();

		//Adiciona o suporte a tooltips
		$('.conteudo').tooltip({
			selector: "[rel=tooltip]"
		});

		$('#selecionar_todos').click(function() {
			if (imobiliaria != '') {
				var parceiros = new Array();
				if ($(this).is(':checked')) {
					$('.parceiro_sim input').attr('checked', true);
					$('.parceiro_sim input').each(function(i) {
						parceiros[i] = $(this).val();
					});
					ids = parceiros.join();
					opcao = 'adicionar';

					selecionados = total;
					$('#selecionados').html(selecionados);
				} else {
					$('.parceiro_nao input').attr('checked', true);
					$('.parceiro_nao input').each(function(i) {
						parceiros[i] = $(this).val();
					});
					refs = parceiros.join();
					opcao = 'remover';
					ids = '';

					selecionados = 0;
					$('#selecionados').html(selecionados);
				}

				$.ajax({
					type: 'POST',
					async: false,
					url: 'selecionar_todos',
					data: {
						o: opcao,
						imob: imobiliaria,
						ids: ids,
						pg: pg,
						todos: 1
					}
				});
			} else {
				alert('Selecione a imobiliária');
				$('#selecionar_todos').attr('checked', false);
			}
		});

		//-----------------------------------------------------------------------

		$('.selecionar-todos-filtro').click(function() {
			var ids = '';
			var referencias = ''
			var separador = '';
			if (imobiliaria != '') {
				$('.parceiro_sim').find('input').each(function() {
					ids += separador + $(this).val();
					referencias += separador + $(this).attr('data-referencia');
					separador = ',';
				});
				$.ajax({
					type: 'POST',
					async: true,
					url: 'adicionar_batch',
					dataType: 'html',
					data: {
						imob: imobiliaria,
						id: ids,
						referencia: referencias,
						pg: pg
					},
					success: function(xml) {
						$('.parceiro_sim input').attr('checked', true);
						$.ajax({
							type: 'GET',
							async: true,
							dataType: 'html',
							url: 'selecionados/' + imobiliaria + '/' + pg,
							success: function(selecionados) {
								$('#selecionados').html(selecionados);
							}
						});
					},
					beforeSend: function() {
						$('.loading').addClass('active');
					},
					complete: function() {
						$('.loading').removeClass('active');
					}
				});
			}
		});

		//-----------------------------------------------------------------------

		$('.remover-todos-filtro').click(function() {
			var ids = '';
			var referencias = ''
			var separador = '';
			if (imobiliaria != '') {
				$('.parceiro_nao').find('input').each(function() {
					ids += separador + $(this).val();
					referencias += separador + $(this).attr('data-referencia');
					separador = ',';
				});
				$.ajax({
					type: 'POST',
					async: true,
					dataType: 'html',
					url: 'remover_batch',
					data: {
						imob: imobiliaria,
						id: ids,
						referencia: referencias,
						pg: pg
					},
					success: function(xml) {
						$('.parceiro_nao input').attr('checked', true);
						$.ajax({
							type: 'GET',
							async: true,
							url: 'selecionados/' + imobiliaria + '/' + pg,
							dataType: 'html',
							success: function(selecionados) {
								$('#selecionados').html(selecionados);
								console.log('ccc');
							}
						});
					},
					beforeSend: function() {
						$('.loading').addClass('active');
					},
					complete: function() {
						$('.loading').removeClass('active');
					}
				});
			}
		});

		//-----------------------------------------------------------------------

		$('.date').datepicker({
			autoclose: true
		});

		//-----------------------------------------------------------------------

		$('.parceiro_sim').find('input').click(function() {
			if (imobiliaria != '') {
				$.ajax({
					type: 'POST',
					async: true,
					url: 'adicionar',
					data: {
						imob: imobiliaria,
						id: $(this).val(),
						referencia: $(this).attr('data-referencia'),
						pg: pg
					},
					beforeSend: function() {
						$('.loading').addClass('active');
					},
					complete: function() {
						$('.loading').removeClass('active');
					}
				});
			}
		});

		//-----------------------------------------------------------------------

		$('.parceiro_nao').find('input').click(function() {
			if (imobiliaria != '') {
				$.ajax({
					type: 'POST',
					async: true,
					url: 'remover',
					data: {
						imob: imobiliaria,
						id: $(this).val(),
						referencia: $(this).attr('data-referencia'),
						pg: pg
					},
					beforeSend: function() {
						$('.loading').addClass('active');
					},
					complete: function() {
						$('.loading').removeClass('active');
					}
				});
			}
		});

		//-----------------------------------------------------------------------

		$('.duplicar').click(function() {
			id = $(this).attr('data-id');
			$('.esconder' + id).html('');
			var remover = '<a href="javascript:void(0)" onclick="removerImovelOrulo(' + id + ')" class="remover" data-id="' + id + '">';
			remover += '<i class="icon-remove icon-white"></i></a>';
			$(this).parent().html(remover);
		});

		//-----------------------------------------------------------------------

		$('#parceiro_id').on('change', function() {

			if ($(this).val() != '') {
				window.location = '<?= parse_url($_SERVER["REQUEST_URI"], PHP_URL_PATH) ?>?parceiro=' + $(this).val();
			}

			$.ajax({
				dataType: 'json',
				url: site_url + '/admin/imovelparceiro/tipos/' + $(this).val() + '?r=json'
			}).done(function(data) {
				$('#tipo_id').html('');
				var r = '<option selected="selected" value="">Selecione</option>';
				$.each(data, function(id, val) {
					r += '<option value="' + id + '">' + val + '</option>';
				});
				$('#tipo_id').html(r);
			});
		});

		//-----------------------------------------------------------------------

		$('.btn-excel').click(function() {

			if ($('#site_parceiro').val() == '') {
				alert('Selecione o site parceiro');
			} else {
				return;
				var el = $('#buscar-relatorio');

				el.attr({
					'action': el.attr('action').replace('exportacao/selecionar', 'exportacao/selecionar?format=excel'),
					'target': '_blank'
				});

				el.submit();

				el.attr({
					'action': el.attr('action').replace('?format=excel', ''),
					'target': ''
				});
			}
		});
	});

	//--------------------------------------------------------------------------

	function cidadeBairro(cidade, qual_select) {
		//Função que preenche um select de bairro ao selecionar um determinada cidade
		//qual_select - informa qual o id do select que contém os bairros

		$.getJSON(site_url + '/bairro/busca_por_cidade/' + cidade, function(data) {
			$('#' + qual_select + ' option').each(function() {
				$(this).remove();
			});
			$('#' + qual_select).append('<option value="">Todos</option>');

			$.each(data, function(key, val) {
				$('#' + qual_select).append('<option value="' + data[key].id + '">' + data[key].bairro + '</option>');
			});
		});
	}

	//--------------------------------------------------------------------------

	function estadoCidade(uf, qual_select) {
		//Função que preenche um select de cidade ao selecionar um determinado estado
		//qual_select - informa qual o id do select que contém as cidades

		$.getJSON(site_url + '/cidade/busca_por_estado/' + uf, function(data) {
			$('#' + qual_select + ' option').each(function() {
				$(this).remove();
			});
			$('#' + qual_select).append('<option value="">Todas</option>');

			$.each(data, function(key, val) {
				$('#' + qual_select).append('<option value="' + data[key].id + '">' + data[key].cidade + '</option>');
			});
		});
	}

	//--------------------------------------------------------------------------

	function removerImovel(id, referencia) {
		if (imobiliaria != '') {
			$.ajax({
				type: 'POST',
				async: true,
				url: 'remover',
				data: {
					imob: imobiliaria,
					id: id,
					referencia: referencia,
					pg: pg
				},
				beforeSend: function() {
					$('.loading').addClass('active');
				},
				complete: function() {
					$('.loading').removeClass('active');
					$('#imovel' + id + '_nao').attr('checked', true);
				}
			});
		}
	}

	//--------------------------------------------------------------------------

	function removerImovelOrulo(id) {
		$.ajax({
			type: 'GET',
			async: true,
			dataType: 'html',
			url: site_url + '/admin/imovel/deletar_orulo/' + id,
			success: function(selecionados) {
				var duplicar = '<a href="' + site_url + '/admin/imovel/duplicar?id=' + id + '" class="duplicar" target="_blank" data-id="' + id + '">';
				duplicar += '<i class="icon-plus-sign icon-white"></i></a>';
				$('#deletar' + id).html(duplicar)
			}
		});
	}

	//--------------------------------------------------------------------------

	function selecionarImovel(id, referencia) {
		if (imobiliaria != '') {
			$.ajax({
				type: 'POST',
				async: false,
				url: 'adicionar',
				data: {
					imob: imobiliaria,
					id: id,
					referencia: referencia,
					pg: pg
				},
				success: function(xml) {
					$('#imovel' + id + '_sim').attr('checked', true);
				}
			});
		}
	}

	//--------------------------------------------------------------------------

	function validarBusca() {
		if ($('#parceiro_id').val() == '') {
			alert('Selecione a imobiliária');
			$('#parceiro_id').focus();
			return false
		}
		if ($('#pg').val() == '') {
			alert('Selecione a transação');
			$('#pg').focus();
			return false
		}
	}

	//--------------------------------------------------------------------------

	function verFicha(pg, id) {
		//Verificar se as urls tem / no final e se tem index.php

		url = site_url;
		if (url.slice(-1) == '/') url = url.substring(0, url.length - 1);
		if (url.slice(-9) == 'index.php') url = url.substring(0, url.length - 9);
		if (url.slice(-1) == '/') url = url.substring(0, url.length - 1);
		window.open(url + '/index.php/imovel/' + pg + '/' + id);
	}
</script>


</body>

</html>